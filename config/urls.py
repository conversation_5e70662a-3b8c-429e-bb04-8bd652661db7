"""
Django URL配置文件。

该文件负责将不同的URL路径映射到相应的视图函数或类视图上。它使用Django的路径转换器和正则表达式来定义URL模式。
"""

from django.contrib import admin
from django.urls import include, path
from drf_spectacular.views import (
    SpectacularAPIView,
    SpectacularRedocView,
    SpectacularSwaggerView,
)
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
    TokenVerifyView,
)


# URL模式列表，用于将URL路径映射到对应的视图或类视图
urlpatterns = [
    # Django管理员站点的URL配置
    path("admin/", admin.site.urls),
    # Djoser用户管理API的URL配置
    path("auth/", include("djoser.urls")),  # Djoser 的核心路由
    path("auth/", include("djoser.urls.jwt")),
    # API文档
    path(
        "api/schema/",
        SpectacularAPIView.as_view(
            api_version="v1",
            urlconf=None,  # 自动检测urls
        ),
        name="schema",
    ),
    path(
        "api/schema/swagger-ui/",
        SpectacularSwaggerView.as_view(url_name="schema"),
        name="swagger-ui",
    ),
    path(
        "api/schema/redoc/",
        SpectacularRedocView.as_view(url_name="schema"),
        name="redoc",
    ),
    # 版本化API的URL配置
    path("api/v1/", include("apps.member.urls"), name="member"),
    path("api/v1/", include("apps.app.urls"), name="app"),
    path("api/v1/", include("apps.feedback.urls"), name="feedback"),
    path("api/v1/", include("apps.channel.urls"), name="channel"),
    path("api/v1/", include("apps.repay.urls"), name="repay"),
    path("api/v1/", include("apps.website.urls"), name="website"),
]
