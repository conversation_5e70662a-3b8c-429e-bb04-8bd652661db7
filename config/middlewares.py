# 创建一个自定义的中间件类，用于拦截异常
class ExceptionMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        try:
            # 让请求继续传递给下一个中间件或视图
            response = self.get_response(request)
            return response
        except Exception as e:
            # 捕获异常并按格式输出
            error_message = str(e)
            # 您可以根据需要定义自己的错误格式
            error_response = {
                'error': error_message
            }
            # 创建一个HttpResponse对象，将错误信息以JSON格式返回
            from django.http import JsonResponse
            return JsonResponse(error_response, status=500)
