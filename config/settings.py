"""
Django配置文件 for config项目。

由'django-admin startproject'生成，使用Django 4.2.4。

更多信息请参见：
https://docs.djangoproject.com/en/4.2/topics/settings/
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

# 导入操作系统模块，用于获取环境变量
import os
import sys

# 导入datetime模块，用于设置token过期时间
from datetime import timedelta

# 导入pathlib模块，用于操作文件路径
from pathlib import Path

from dotenv import load_dotenv

# 导入Django设置模块，用于从当前模块中导入设置

# 导入自定义的TencentCOSStorage类，用于设置默认文件存储为腾讯云对象存储

# 加载.env文件
load_dotenv()

# 构建到 .env 文件的路径
ENV_PATH = Path(__file__).resolve().parent.parent / ".env"

# 根据 DJANGO_ENV 环境变量加载对应的 .env 文件
django_env = os.getenv("DJANGO_ENV", "develop")

# 加载不同环境的 .env 文件
load_dotenv(ENV_PATH.with_name(f".env.{django_env}"))

# 项目根目录路径
BASE_DIR = Path(__file__).resolve().parent.parent

# 添加自定义应用路径
sys.path.insert(0, os.path.join(BASE_DIR, "apps"))

# 快速开发设置，不适合生产环境
# 设置SECRET_KEY，用于Django的加密功能
# SECURITY WARNING: keep the secret key used in production secret!
# SECRET_KEY = 'django-insecure-d%nq=f!$fp@shs)gduu)t8th4y^+&3^x-u=t#q$ebs&8)4!58z'
SECRET_KEY = os.environ.get(
    "DJANGO_SECRET_KEY",
    "django-insecure-d%nq=f!$fp@shs)gduu)t8th4y^+&3^x-u=t#q$ebs&8)4!58z",
)

# 设置DEBUG，用于开启或关闭调试模式
# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.getenv("DEBUG") == "True"

# 允许的主机列表，*表示允许任何主机访问
ALLOWED_HOSTS = ["*"]

# 设置跨站请求伪造的可信来源
CSRF_TRUSTED_ORIGINS = ["https://repay.mircool.cn"]

# 安装的应用列表
INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # 'ninja',  # Ninja JWT
    # 'ninja_jwt',  # Ninja JWT
    "rest_framework",  # Django REST framework
    "rest_framework_simplejwt",  # Django REST framework JWT
    "drf_spectacular",
    "drf_spectacular_sidecar",
    "djoser",
    "django5_upyun_storage",
    # 自定义应用
    "apps.index",  # 首页应用
    "apps.member",
    "apps.repay",
    "apps.feedback",
    "apps.channel",
    "apps.app",
    "apps.website",
    'django_filters',
]

# 中间件列表
MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "config.middlewares.ExceptionMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
]

# 配置URLs的模块
ROOT_URLCONF = "config.urls"

# 模板配置
TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / "templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

# WSGI应用配置
WSGI_APPLICATION = "config.wsgi.application"

# 数据库配置
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": os.getenv("DATABASE_NAME", "repay_django"),
        "USER": os.getenv("DATABASE_USER", "repay_django"),
        "PASSWORD": os.getenv("DATABASE_PASSWORD", "zNTeCafJQ5H4QAfA"),
        "HOST": os.getenv("DATABASE_HOST", "*************"),
        "PORT": os.getenv("DATABASE_PORT", "33060"),
        "OPTIONS": {
            "charset": "utf8mb4",  # 字符集
        },
    }
}

# 配置sqlite3数据库
# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.sqlite3',
#         'NAME': BASE_DIR / 'data/db9335c27f086f24ba.sqlite3',  # 将数据库文件保存在data目录下
#     }
# }


# 密码验证器列表
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# 国际化设置
LANGUAGE_CODE = "zh-hans"
TIME_ZONE = "Asia/Shanghai"
USE_I18N = True
USE_TZ = False

# 静态文件配置
STATIC_URL = "static/"
STATIC_ROOT = BASE_DIR / "static"

# 媒体文件配置
MEDIA_URL = "/media/"
MEDIA_ROOT = os.path.join(BASE_DIR, "media")

# 默认主键字段类型
DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# 自定义用户模型
AUTH_USER_MODEL = "member.Member"

# JWT配置
# NINJA_JWT = {
#     'ACCESS_TOKEN_LIFETIME': timedelta(days=1),
#     'REFRESH_TOKEN_LIFETIME': timedelta(days=30),
#     'UPDATE_LAST_LOGIN': True,
#
#     'SIGNING_KEY': settings.SECRET_KEY,
# }

# 日志配置
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "handlers": {
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
        },
    },
    "root": {
        "handlers": ["console"],
        "level": "DEBUG",
    },
}

# 微信小程序配置
WECHAT_URL = "https://api.weixin.qq.com/sns/jscode2session"  # 微信登录接口
WECHAT_APPID = "wx4c5cf65ff2b1e130"  # 微信小程序的appid
WECHAT_APPSECRET = "dcb3c8ec11c4c7aef08d1ac41903c7a9"  # 微信小程序的secret

# 腾讯云COS配置
# DEFAULT_FILE_STORAGE = "utils.django_cos_storage.TencentCOSStorage"
# TENCENTCOS_STORAGE = {
#     "BUCKET": "repay-django-1251001265",
#     "CONFIG": {
#         "Region": "ap-chengdu",
#         "SecretId": "AKIDoq2ItYyvU9tm4CANawFnDRlhmJO3gmoS",
#         "SecretKey": "9Z6raWn9HETY1fzEpYBkFSF45l7DyGcE",
#     }
# }

REST_FRAMEWORK = {
    "EXCEPTION_HANDLER": "apps.core.utils.custom_exception_handler",
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework_simplejwt.authentication.JWTAuthentication",
        "rest_framework.authentication.SessionAuthentication",
        "rest_framework.authentication.BasicAuthentication",
    ),
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAuthenticated",
    ],
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
    "DEFAULT_FILTER_BACKENDS": ['django_filters.rest_framework.DjangoFilterBackend'],
}

# 配置jwt
SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(days=1),  # Access Token 有效期
    "REFRESH_TOKEN_LIFETIME": timedelta(days=30),  # Refresh Token 有效期
    "ROTATE_REFRESH_TOKENS": True,  # 每次使用刷新令牌时生成新的令牌
    "BLACKLIST_AFTER_ROTATION": True,  # 刷新后是否禁用旧的令牌
    "AUTH_HEADER_TYPES": ("Bearer",),  # Token 前缀类型
}

DJOSER = {
    "USER_CREATE_PASSWORD_RETYPE": True,  # 注册时要求重复密码
    "TOKEN_MODEL": None,  # 禁用默认的 Token 模型，完全使用 JWT
    "SERIALIZERS": {
        "user_create": "djoser.serializers.UserCreateSerializer",  # 注册时使用的序列化器
        "user": "apps.member.serializers.CustomUserSerializer",  # 用户详情序列化器
        "current_user": "apps.member.serializers.CustomUserSerializer",  # 当前用户详情
        "token_create": "djoser.serializers.TokenCreateSerializer",  # 登录序列化器
    },
}

# 设置为默认存储器
STORAGES = {
    "default": {
        "BACKEND": "django5_upyun_storage.storage.UpYunStorage",
    },
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
}

# 配置静态文件存储
# STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

SPECTACULAR_SETTINGS = {
    "TITLE": "随钱记API",
    "DESCRIPTION": "随钱记小程序后端API",
    "VERSION": "1.0.0",
    "SERVE_INCLUDE_SCHEMA": False,
    "SWAGGER_UI_DIST": "SIDECAR",
    "SWAGGER_UI_FAVICON_HREF": "SIDECAR",
    "REDOC_DIST": "SIDECAR",
    # 添加认证配置
    "SECURITY": [{"Bearer": []}],
    "COMPONENT_SPLIT_REQUEST": True,
    "SCHEMA_PATH_PREFIX": "/api/v1",
}

UPYUN_STORAGE = {
    "SERVICE": "media-suiqianji",
    "USERNAME": "suiqianjiroot",
    "PASSWORD": "Gm8yls8u17FuUm4WoLTmjrjzxKtMAIDq",
    "DOMAIN": "https://media.suiqianji.mircool.cn",  # 可选，如果为空，则使用又拍云的默认域名 http://yourdomain.com
}

# 又拍云短信配置
UPYUN_SMS_TOKEN = os.getenv("UPYUN_SMS_TOKEN")  # 需要替换为实际的token
UPYUN_SMS_TEMPLATE_ID = os.getenv("UPYUN_SMS_TEMPLATE_ID")  # 需要替换为实际的模板ID

# Celery配置
CELERY_BROKER_URL = os.getenv("CELERY_BROKER_URL")
CELERY_RESULT_BACKEND = os.getenv("CELERY_RESULT_BACKEND")
CELERY_TIMEZONE = "Asia/Shanghai"
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT = 30 * 60

# Spug短信配置
SPUG_USER_ID = os.getenv("SPUG_USER_ID")  # Spug用户ID，用于个人模式发送短信
SPUG_REPAYMENT_TEMPLATE_ID = os.getenv("SPUG_REPAYMENT_TEMPLATE_ID")  # 还款提醒短信模板ID
SPUG_OVERDUE_TEMPLATE_ID = os.getenv("SPUG_OVERDUE_TEMPLATE_ID")  # 逾期提醒短信模板ID
