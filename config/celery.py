import os
from celery import Celery
from celery.schedules import crontab

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

# 创建Celery实例
app = Celery('suiqianji')

# 使用Django的settings文件配置Celery
app.config_from_object('django.conf:settings', namespace='CELERY')

# 自动发现任务
app.autodiscover_tasks()

# 配置定时任务
app.conf.beat_schedule = {
    'send-repayment-reminder': {
        'task': 'apps.repay.tasks.send_repayment_reminder',
        'schedule': crontab(hour=9, minute=0),  # 每天早上9点执行
    },
} 