[project]
name = "djangoproject"
version = "0.1.0"
description = "Add your description here"
requires-python = ">=3.10"
dependencies = [
    "django>=5.2",
    "django-filter>=25.1",
    "django5-upyun-storage>=1.0.4",
    "djangorestframework>=3.16.0",
    "djangorestframework-simplejwt>=5.5.0",
    "djoser>=2.3.1",
    "dotenv>=0.9.9",
    "drf-spectacular>=0.28.0",
    "drf-spectacular-sidecar>=2025.4.1",
    "httpx>=0.28.1",
    "mysqlclient>=2.2.7",
    "pillow>=11.1.0",
    "pypinyin>=0.54.0",
    "python-dateutil>=2.9.0.post0",
    "whitenoise>=6.9.0",
]
