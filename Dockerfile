# 指定基础镜像为 Python 3.10.14 的 Alpine 版本
FROM python:3.10.14-alpine3.20

# 设置工作目录为 /app
WORKDIR /app

# 安装系统依赖
RUN apk add --no-cache \
    mariadb-dev \
    gcc \
    musl-dev \
    libffi-dev \
    pkgconfig \
    supervisor

# 创建日志目录
RUN mkdir -p /var/log/supervisor

# 将 requirements.txt 文件复制到容器的 /app 目录下
COPY requirements.txt /app/

# 安装 Python 依赖，可以通过更换国内源加速安装,更换成阿里云镜像
RUN pip install --upgrade pip -i https://pypi.mirrors.ustc.edu.cn/simple/

# 安装 Python 依赖
RUN pip install -r requirements.txt -i https://pypi.mirrors.ustc.edu.cn/simple/

# 将当前目录下的所有文件复制到容器的 /app 目录下
COPY . /app/

# 将容器的 8000 端口暴露给外部
EXPOSE 8000

# 收集静态文件
RUN python manage.py collectstatic --noinput

# 复制supervisor配置文件
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# 使用supervisor启动所有服务
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
