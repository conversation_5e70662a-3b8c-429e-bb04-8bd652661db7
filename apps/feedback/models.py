from django.contrib.auth import get_user_model
from django.db import models

from config.models import BaseModel

User = get_user_model()


# Create your models here.
class Feedback(BaseModel):
    """反馈"""
    content = models.TextField(verbose_name='反馈内容')
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')

    class Meta:
        verbose_name = '反馈'
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.content
