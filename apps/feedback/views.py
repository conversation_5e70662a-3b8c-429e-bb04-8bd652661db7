from rest_framework import permissions, status, viewsets, views
from rest_framework.response import Response
from rest_framework_simplejwt.authentication import J<PERSON><PERSON>uthentication
from rest_framework.decorators import api_view

from .models import Feedback
from .serializers import FeedbackSerializer, FeedbackInputSerializer, FeedbackOutputSerializer


class FeedbackViewSet(viewsets.ModelViewSet):
    queryset = Feedback.objects.all()
    serializer_class = FeedbackSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # 只返回当前登录用户的反馈
        return self.queryset.filter(user=self.request.user)

    def create(self, request, *args, **kwargs):
        # 保存手机号码到用户表
        request.user.mobile = request.data.get('mobile')
        request.user.save()

        # 调用父类的 create 方法
        response = super().create(request, *args, **kwargs)

        # 返回自定义的响应格式
        return Response({
            'code': 10000,
            'message': '更新成功',
            'data': response.data
        }, status=status.HTTP_201_CREATED)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)


class FeedbackCreateView(views.APIView):
    """创建反馈"""
    def post(self, request):
        serializer = FeedbackInputSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({"code": 400, "message": "参数错误", "data": serializer.errors})

        # 添加反馈
        feedback = Feedback.objects.create(
            member=request.user,
            **serializer.validated_data
        )

        output_serializer = FeedbackOutputSerializer(feedback)
        return Response({
            "code": 200,
            "message": "success",
            "data": output_serializer.data
        })
