from rest_framework import serializers

from .models import Feedback


class FeedbackSerializer(serializers.ModelSerializer):
    mobile = serializers.CharField(source="user.mobile", read_only=True)

    class Meta:
        model = Feedback
        fields = ["id", "content", "mobile", "create_time", "updated_at"]


class FeedbackInputSerializer(serializers.Serializer):
    content = serializers.CharField(max_length=200, min_length=5, help_text="反馈内容")
    mobile = serializers.RegexField(regex=r"^1[3456789]\d{9}$", help_text="手机号码")


class FeedbackOutputSerializer(serializers.ModelSerializer):
    class Meta:
        model = Feedback
        fields = "__all__"
