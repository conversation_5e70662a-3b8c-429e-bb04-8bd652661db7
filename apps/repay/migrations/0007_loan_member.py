# Generated by Django 4.2.4 on 2023-08-15 17:54

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('repay', '0006_alter_loan_remark'),
    ]

    operations = [
        migrations.AddField(
            model_name='loan',
            name='member',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='所属用户'),
            preserve_default=False,
        ),
    ]
