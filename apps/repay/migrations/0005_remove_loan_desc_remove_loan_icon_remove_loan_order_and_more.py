# Generated by Django 4.2.4 on 2023-08-15 17:37

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('channel', '0003_alter_channel_icon'),
        ('repay', '0004_alter_loan_options'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='loan',
            name='desc',
        ),
        migrations.RemoveField(
            model_name='loan',
            name='icon',
        ),
        migrations.RemoveField(
            model_name='loan',
            name='order',
        ),
        migrations.RemoveField(
            model_name='loan',
            name='quota',
        ),
        migrations.RemoveField(
            model_name='loan',
            name='term_unit',
        ),
        migrations.RemoveField(
            model_name='repayment',
            name='amount',
        ),
        migrations.RemoveField(
            model_name='repayment',
            name='period',
        ),
        migrations.RemoveField(
            model_name='repayment',
            name='repayment_time',
        ),
        migrations.AddField(
            model_name='loan',
            name='amount',
            field=models.PositiveIntegerField(default=0, verbose_name='贷款金额'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='loan',
            name='interest_sum',
            field=models.FloatField(default=0, verbose_name='利息总额'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='loan',
            name='loan_date',
            field=models.DateField(default=django.utils.timezone.now, verbose_name='贷款日期'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='loan',
            name='remark',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='备注'),
        ),
        migrations.AddField(
            model_name='loan',
            name='repayment_method',
            field=models.CharField(choices=[('1', '等额本息'), ('2', '等额本金')], default=1, max_length=20, verbose_name='还款方式'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='repayment',
            name='capital',
            field=models.FloatField(default=0, verbose_name='本金'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='repayment',
            name='current_period',
            field=models.IntegerField(default=1, verbose_name='当前期数'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='repayment',
            name='interest',
            field=models.FloatField(default=1, verbose_name='利息'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='repayment',
            name='remaining_principal',
            field=models.FloatField(default=0, verbose_name='剩余本金'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='repayment',
            name='repayment_date',
            field=models.DateField(default=django.utils.timezone.now, verbose_name='还款日期'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='repayment',
            name='total_capital_interest',
            field=models.FloatField(default=0, verbose_name='本息合计'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='repayment',
            name='total_interest',
            field=models.FloatField(default=0, verbose_name='累计利息'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='loan',
            name='channel',
            field=models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='channel.channel', verbose_name='渠道'),
        ),
        migrations.AlterField(
            model_name='loan',
            name='rate',
            field=models.FloatField(verbose_name='贷款利率'),
        ),
        migrations.AlterField(
            model_name='loan',
            name='status',
            field=models.BooleanField(default=False, verbose_name='贷款状态'),
        ),
        migrations.AlterField(
            model_name='loan',
            name='term',
            field=models.PositiveIntegerField(verbose_name='贷款期限'),
        ),
        migrations.AlterField(
            model_name='repayment',
            name='loan',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='repay.loan', verbose_name='贷款项目'),
        ),
        migrations.AlterField(
            model_name='repayment',
            name='status',
            field=models.BooleanField(default=False, verbose_name='状态'),
        ),
    ]
