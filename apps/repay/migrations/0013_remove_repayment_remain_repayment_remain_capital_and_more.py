# Generated by Django 4.2.4 on 2023-08-18 07:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('repay', '0012_remove_repayment_remaining_principal_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='repayment',
            name='remain',
        ),
        migrations.AddField(
            model_name='repayment',
            name='remain_capital',
            field=models.FloatField(default=0, verbose_name='剩余本金'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='repayment',
            name='remain_interest',
            field=models.FloatField(default=0, verbose_name='剩余利息'),
            preserve_default=False,
        ),
    ]
