# Generated by Django 4.2.4 on 2023-08-15 11:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Channel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=20, verbose_name='渠道名称')),
                ('icon', models.ImageField(upload_to='channel', verbose_name='渠道图标')),
                ('status', models.BooleanField(default=True, verbose_name='状态')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '渠道',
                'verbose_name_plural': '渠道',
            },
        ),
        migrations.CreateModel(
            name='Loan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=20, verbose_name='项目名称')),
                ('icon', models.ImageField(upload_to='loan', verbose_name='项目图标')),
                ('desc', models.TextField(verbose_name='项目介绍')),
                ('quota', models.IntegerField(verbose_name='项目额度')),
                ('rate', models.FloatField(verbose_name='项目利率')),
                ('term', models.IntegerField(verbose_name='项目期限')),
                ('term_unit', models.CharField(max_length=10, verbose_name='项目期限单位')),
                ('status', models.IntegerField(default=0, verbose_name='状态')),
                ('order', models.IntegerField(default=0, verbose_name='排序')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('channel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='repay.channel', verbose_name='渠道')),
            ],
            options={
                'verbose_name': '项目',
                'verbose_name_plural': '项目',
            },
        ),
        migrations.CreateModel(
            name='Repayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.FloatField(verbose_name='还款金额')),
                ('period', models.IntegerField(verbose_name='还款期数')),
                ('repayment_time', models.DateTimeField(verbose_name='还款时间')),
                ('status', models.IntegerField(default=0, verbose_name='状态')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('loan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='repay.loan', verbose_name='项目')),
            ],
            options={
                'verbose_name': '还款计划',
                'verbose_name_plural': '还款计划',
            },
        ),
    ]
