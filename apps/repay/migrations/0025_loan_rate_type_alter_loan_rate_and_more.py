# Generated by Django 5.1.3 on 2024-12-07 17:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("repay", "0024_alter_loan_repayment_method_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="loan",
            name="rate_type",
            field=models.CharField(
                choices=[
                    ("year", "年化利率"),
                    ("month", "月利率"),
                    ("day", "日利率"),
                    ("none", "无利息"),
                ],
                default="year",
                max_length=10,
                verbose_name="利率类型",
            ),
        ),
        migrations.AlterField(
            model_name="loan",
            name="rate",
            field=models.FloatField(verbose_name="贷款利率(年化)"),
        ),
        migrations.AlterField(
            model_name="repayment",
            name="remain_capital",
            field=models.FloatField(verbose_name="剩余未还本金"),
        ),
    ]
