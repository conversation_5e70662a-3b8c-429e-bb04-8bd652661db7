# Generated by Django 5.1.3 on 2024-12-19 19:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('repay', '0027_alter_loan_interest_sum'),
    ]

    operations = [
        migrations.AddField(
            model_name='loan',
            name='early_repayment_amount',
            field=models.FloatField(blank=True, null=True, verbose_name='提前还款金额'),
        ),
        migrations.AddField(
            model_name='loan',
            name='early_repayment_date',
            field=models.DateField(blank=True, null=True, verbose_name='提前还款日期'),
        ),
        migrations.AddField(
            model_name='loan',
            name='early_repayment_saved_interest',
            field=models.FloatField(blank=True, null=True, verbose_name='提前还款节省利息'),
        ),
        migrations.AddField(
            model_name='loan',
            name='early_repayment_type',
            field=models.CharField(blank=True, choices=[('full', '全部还款'), ('partial', '部分还款')], max_length=10, null=True, verbose_name='提前还款类型'),
        ),
        migrations.AddField(
            model_name='loan',
            name='is_early_repayment',
            field=models.BooleanField(default=False, verbose_name='是否提前还款'),
        ),
        migrations.AddField(
            model_name='repayment',
            name='actual_repayment_amount',
            field=models.FloatField(blank=True, null=True, verbose_name='实际还款金额'),
        ),
        migrations.AddField(
            model_name='repayment',
            name='actual_repayment_date',
            field=models.DateField(blank=True, null=True, verbose_name='实际还款日期'),
        ),
        migrations.AddField(
            model_name='repayment',
            name='is_early_repayment',
            field=models.BooleanField(default=False, verbose_name='是否提前还款'),
        ),
        migrations.AddField(
            model_name='repayment',
            name='saved_interest',
            field=models.FloatField(blank=True, null=True, verbose_name='节省利息'),
        ),
        migrations.AlterField(
            model_name='loan',
            name='repayment_method',
            field=models.IntegerField(choices=[(1, '等额本息'), (2, '等额本金'), (3, '先息后本'), (4, '一次性还本付息')], verbose_name='还款方式'),
        ),
    ]
