from decimal import ROUND_HALF_UP, Decimal
from dateutil.relativedelta import relativedelta
from django.db import transaction
from django.db.models import Q, Sum
from django.utils import timezone

from .models import Loan, Repayment

class RepaymentService:
    """还款服务类,处理还款相关的业务逻辑"""
    
    @staticmethod
    def generate_repayment_plans(
        amount: Decimal,
        rate: Decimal,
        term: int,
        repayment_method: int,
        loan_date,
        due_date=None
    ):
        """
        生成还款计划
        
        Args:
            amount: 贷款金额
            rate: 年化利率
            term: 期数
            repayment_method: 还款方式(1-等额本息,2-等额本金,3-先息后本,4-一次性还本付息)
            loan_date: 放款日期
            due_date: 到期日期(一次性还本付息时必填)
            
        Returns:
            tuple: (还款计划列表, 总利息)
        """
        repayment_plans = []
        interest_sum = Decimal("0")

        if repayment_method == 4:  # 一次性还本付息
            days = (due_date - loan_date).days
            
            if rate == Decimal("0"):
                interest = Decimal("0")
            else:
                interest = (amount * rate * Decimal(str(days)) / (Decimal("100") * Decimal("365"))).quantize(
                    Decimal("0.01"), rounding=ROUND_HALF_UP
                )
            
            repayment_plans.append(
                {
                    "current_period": 1,
                    "repayment_date": due_date,
                    "capital": float(amount),
                    "interest": float(interest),
                    "total_capital_interest": float(amount + interest),
                    "remain_capital": float(amount),
                    "total_interest": float(interest),
                    "status": False,
                }
            )
            interest_sum = interest
            
        elif repayment_method == 1:  # 等额本息
            if rate == Decimal("0"):
                monthly_payment = (amount / Decimal(str(term))).quantize(
                    Decimal("0.01"), rounding=ROUND_HALF_UP
                )
                
                remaining_principal = amount
                for period in range(1, term + 1):
                    repayment_date = loan_date + relativedelta(months=period)
                    
                    if period == term:
                        principal = remaining_principal
                    else:
                        principal = monthly_payment
                    
                    repayment_plans.append(
                        {
                            "current_period": period,
                            "repayment_date": repayment_date,
                            "capital": float(principal),
                            "interest": 0,
                            "total_capital_interest": float(principal),
                            "remain_capital": float(remaining_principal),
                            "total_interest": 0,
                            "status": False,
                        }
                    )
                    remaining_principal -= principal
            else:
                monthly_rate = rate / Decimal("100") / Decimal("12")
                remaining_principal = amount
                accumulated_interest = Decimal("0")
                
                monthly_payment = (
                    amount
                    * monthly_rate
                    * (Decimal("1") + monthly_rate) ** term
                    / ((Decimal("1") + monthly_rate) ** term - Decimal("1"))
                ).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
                
                total_payment = monthly_payment * term
                interest_sum = total_payment - amount

                for period in range(1, term + 1):
                    interest = (remaining_principal * monthly_rate).quantize(
                        Decimal("0.01"), rounding=ROUND_HALF_UP
                    )
                    principal = (monthly_payment - interest).quantize(
                        Decimal("0.01"), rounding=ROUND_HALF_UP
                    )
                    if period == term:
                        principal = remaining_principal

                    repayment_date = loan_date + relativedelta(months=period)
                    accumulated_interest += interest

                    repayment_plans.append(
                        {
                            "current_period": period,
                            "repayment_date": repayment_date,
                            "capital": float(principal),
                            "interest": float(interest),
                            "total_capital_interest": float(principal + interest),
                            "remain_capital": float(remaining_principal),
                            "total_interest": float(accumulated_interest),
                            "status": False,
                        }
                    )
                    remaining_principal -= principal

        elif repayment_method == 2:  # 等额本金
            monthly_principal = (amount / Decimal(str(term))).quantize(
                Decimal("0.01"), rounding=ROUND_HALF_UP
            )
            monthly_rate = rate / Decimal("100") / Decimal("12")
            
            remaining_principal = amount
            accumulated_interest = Decimal("0")
            
            for period in range(1, term + 1):
                interest = (remaining_principal * monthly_rate).quantize(
                    Decimal("0.01"), rounding=ROUND_HALF_UP
                )
                interest_sum += interest
                
                if period == term:
                    principal = remaining_principal
                else:
                    principal = monthly_principal
                
                repayment_date = loan_date + relativedelta(months=period)
                accumulated_interest += interest
                
                repayment_plans.append(
                    {
                        "current_period": period,
                        "repayment_date": repayment_date,
                        "capital": float(principal),
                        "interest": float(interest),
                        "total_capital_interest": float(principal + interest),
                        "remain_capital": float(remaining_principal),
                        "total_interest": float(accumulated_interest),
                        "status": False,
                    }
                )
                remaining_principal -= principal

        elif repayment_method == 3:  # 先息后本
            monthly_rate = rate / Decimal("100") / Decimal("12")
            monthly_interest = (amount * monthly_rate).quantize(
                Decimal("0.01"), rounding=ROUND_HALF_UP
            )
            interest_sum = monthly_interest * term
            
            for period in range(1, term + 1):
                repayment_date = loan_date + relativedelta(months=period)
                principal = amount if period == term else Decimal("0")
                
                repayment_plans.append(
                    {
                        "current_period": period,
                        "repayment_date": repayment_date,
                        "capital": float(principal),
                        "interest": float(monthly_interest),
                        "total_capital_interest": float(principal + monthly_interest),
                        "remain_capital": float(amount),
                        "total_interest": float(monthly_interest * period),
                        "status": False,
                    }
                )

        return repayment_plans, float(interest_sum)

    @staticmethod
    def create_loan_with_repayments(validated_data):
        """创建贷款及其还款计划"""
        amount = Decimal(str(validated_data["amount"]))
        rate = Decimal(str(validated_data["rate"]))
        term = validated_data.get("term")
        repayment_method = validated_data["repayment_method"]
        loan_date = validated_data["loan_date"]
        due_date = validated_data.get("due_date")

        # 生成还款计划
        repayment_plans, interest_sum = RepaymentService.generate_repayment_plans(
            amount=amount,
            rate=rate,
            term=term,
            repayment_method=repayment_method,
            loan_date=loan_date,
            due_date=due_date
        )

        # 创建贷款记录
        with transaction.atomic():
            loan = Loan.objects.create(
                name=validated_data["name"],
                amount=float(amount),
                rate=float(rate),
                term=term,
                due_date=due_date,
                loan_date=loan_date,
                repayment_method=repayment_method,
                rate_type=validated_data["rate_type"],
                interest_sum=interest_sum,
                service_charge=validated_data.get("service_charge", 0),
                channel=validated_data["channel"],
                remark=validated_data.get("remark", ""),
                member=validated_data["member"],
            )

            # 创建还款计划
            for plan in repayment_plans:
                Repayment.objects.create(loan=loan, **plan)

        return loan

    @staticmethod
    def update_loan_with_repayments(instance, validated_data):
        """更新贷款及其还款计划"""
        amount = Decimal(str(validated_data["amount"]))
        rate = Decimal(str(validated_data["rate"]))
        term = validated_data.get("term")
        repayment_method = validated_data["repayment_method"]
        loan_date = validated_data["loan_date"]
        due_date = validated_data.get("due_date")

        # 生成新的还款计划
        repayment_plans, interest_sum = RepaymentService.generate_repayment_plans(
            amount=amount,
            rate=rate,
            term=term,
            repayment_method=repayment_method,
            loan_date=loan_date,
            due_date=due_date
        )

        # 更新贷款记录和还款计划
        with transaction.atomic():
            # 更新��款记录
            for field, value in validated_data.items():
                if field not in ['amount', 'rate', 'interest_sum']:
                    setattr(instance, field, value)
            instance.amount = float(amount)
            instance.rate = float(rate)
            instance.interest_sum = interest_sum
            instance.save()

            # 删除原有还款计划并创建新的
            instance.repayments.all().delete()
            for plan in repayment_plans:
                Repayment.objects.create(loan=instance, **plan)

        return instance

    @staticmethod
    def process_repayment(repayment_id):
        """处理还款"""
        with transaction.atomic():
            repayment = Repayment.objects.select_for_update().get(id=repayment_id)
            
            if repayment.status:
                raise ValueError("该期已还款")

            # 更新还款状态
            repayment.status = True
            repayment.save()

            # 检查是否所有还款计划都已完成
            loan = repayment.loan
            if not loan.repayments.filter(status=False).exists():
                loan.status = True
                loan.save()

        return repayment

    @staticmethod
    def get_loan_statistics(user):
        """获取用户贷款统计数据"""
        today = timezone.now().date()
        thirty_days_later = today + timezone.timedelta(days=30)

        # 使用select_related优化查询
        unpaid_repayments = (
            Repayment.objects.filter(
                loan__member=user,
                loan__status=False,
                status=False
            )
            .select_related("loan")  # 添加select_related
            .only(
                "total_capital_interest", 
                "capital", 
                "interest",
                "loan__id"  # 添加必要的loan字段
            )
        )

        # 使用values和annotate优化聚合查询
        next_month_stats = unpaid_repayments.filter(
            Q(repayment_date__lt=today) |  
            Q(repayment_date__lte=thirty_days_later)
        ).aggregate(
            next_month_amount=Sum("total_capital_interest", default=Decimal("0.00"))
        )

        # 合并所有统计到一个查询
        total_stats = unpaid_repayments.aggregate(
            total_principal=Sum("capital", default=Decimal("0.00")),
            total_interest=Sum("interest", default=Decimal("0.00")),
            total_amount=Sum("total_capital_interest", default=Decimal("0.00"))
        )

        def round_amount(amount):
            return Decimal(str(amount)).quantize(
                Decimal("0.00"),
                rounding=ROUND_HALF_UP
            )

        return {
            "next_month_amount": round_amount(next_month_stats["next_month_amount"]),
            "total_principal": round_amount(total_stats["total_principal"]),
            "total_interest": round_amount(total_stats["total_interest"]), 
            "total_amount": round_amount(total_stats["total_amount"])
        }

    @staticmethod
    def early_repayment(loan_id: int, repayment_type: str, amount: Decimal = None):
        """
        ���前还款
        
        Args:
            loan_id: 贷款ID
            repayment_type: 还款类型 (full - 全部还款, partial - 部分还款)
            amount: 部分还款时的还款金额
            
        Returns:
            dict: 还款结果
        """
        with transaction.atomic():
            loan = Loan.objects.select_for_update().get(id=loan_id)
            today = timezone.now().date()
            
            # 获取未还款的还款计划
            unpaid_repayments = loan.repayments.filter(status=False).order_by('current_period')
            
            if not unpaid_repayments.exists():
                raise ValueError("该贷款已结清")
                
            # 获取当前期数的还款计划
            current_repayment = unpaid_repayments.first()
            remaining_principal = Decimal(str(current_repayment.remain_capital))
            
            # 计算需要支付的利息（当期利息 + 逾期利息）
            required_interest = Decimal("0")
            
            # 如果当前期已逾期，需要计算所有逾期期数的利息
            overdue_repayments = unpaid_repayments.filter(repayment_date__lt=today)
            if overdue_repayments.exists():
                required_interest = Decimal(str(sum(r.interest for r in overdue_repayments)))
            else:
                # 如果没有逾期，只需要支付当期利息
                required_interest = Decimal(str(current_repayment.interest))
            
            # 计算原本应还总利息（用于计算节省的利息）
            original_total_interest = Decimal(str(sum(r.interest for r in unpaid_repayments)))
            
            if repayment_type == "full":
                # 全部还款需要支付：剩余本金 + 当期利息 + 逾期利息
                total_required_amount = remaining_principal + required_interest
                
                # 计算节省的利息 = 原计划总利息 - 需要支付的利息
                saved_interest = original_total_interest - required_interest
                
                # 更新所有未还款计划
                for repayment in unpaid_repayments:
                    repayment.status = True
                    repayment.is_early_repayment = True
                    repayment.actual_repayment_date = today
                    # 只有当期和逾期的还款计划需要支付利息
                    if repayment.repayment_date <= today:
                        repayment.actual_repayment_amount = float(repayment.interest + (repayment.capital if repayment == current_repayment else 0))
                    else:
                        # 后续期数只需要按比例支付本金
                        repayment.actual_repayment_amount = float(repayment.capital if repayment == current_repayment else 0)
                    repayment.saved_interest = float(repayment.interest if repayment.repayment_date > today else 0)
                    repayment.save()
                
                # 更新贷款状态
                loan.status = True
                loan.is_early_repayment = True
                loan.early_repayment_type = repayment_type
                loan.early_repayment_date = today
                loan.early_repayment_amount = float(total_required_amount)
                loan.early_repayment_saved_interest = float(saved_interest)
                loan.save()
                
                return {
                    "message": "全部结清成功",
                    "repaid_amount": float(total_required_amount),
                    "saved_interest": float(saved_interest),
                    "required_interest": float(required_interest),
                    "has_overdue": overdue_repayments.exists()
                }
                
            elif repayment_type == "partial":
                if not amount:
                    raise ValueError("部分还款时必须指定还款金额")
                    
                amount = Decimal(str(amount))
                # 部分还款时，需要先处理逾期利息和当期利息
                if amount <= required_interest:
                    raise ValueError(f"部分还款金额必须大于应还利息{float(required_interest)}")
                
                # 实际可用于还本金的金额
                principal_amount = amount - required_interest
                if principal_amount >= remaining_principal:
                    raise ValueError("超出待还本金金额，请选择全部结清")
                
                # 计算新的剩余本金
                new_remaining_principal = remaining_principal - principal_amount
                
                # 删除原有未还款计划
                unpaid_repayments.delete()
                
                # 生成新的还款计划
                remaining_term = loan.term - loan.repayments.filter(status=True).count()
                new_repayment_plans, new_interest_sum = RepaymentService.generate_repayment_plans(
                    amount=new_remaining_principal,
                    rate=Decimal(str(loan.rate)),
                    term=remaining_term,
                    repayment_method=loan.repayment_method,
                    loan_date=today
                )
                
                # 创建新的还款计划
                current_period = loan.repayments.filter(status=True).count() + 1
                for plan in new_repayment_plans:
                    # 确保所有金额字段都是float类型
                    for key in ['capital', 'interest', 'total_capital_interest', 'remain_capital', 'total_interest']:
                        if key in plan:
                            plan[key] = float(plan[key])
                    plan['loan'] = loan
                    plan['current_period'] = current_period
                    Repayment.objects.create(**plan)
                    current_period += 1
                
                # 记录部分还款信息
                loan.is_early_repayment = True
                loan.early_repayment_type = repayment_type
                loan.early_repayment_date = today
                loan.early_repayment_amount = float(amount)
                # 计算节省的利息 = 原计划总利息 - 新计划总利息 - 已支付利息
                saved_interest = original_total_interest - Decimal(str(new_interest_sum)) - required_interest
                loan.early_repayment_saved_interest = float(saved_interest)
                loan.save()
                
                return {
                    "message": f"部分还款成功，剩余{remaining_term}期将重新计算还款金额",
                    "repaid_amount": float(amount),
                    "saved_interest": float(saved_interest),
                    "remaining_term": remaining_term,
                    "paid_interest": float(required_interest),
                    "paid_principal": float(principal_amount)
                }
            else:
                raise ValueError("不支持的还款类型")
