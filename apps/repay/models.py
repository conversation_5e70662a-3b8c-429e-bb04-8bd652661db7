from django.contrib.auth import get_user_model
from django.db import models

from apps.channel.models import Channel
from config.models import BaseModel

User = get_user_model()


# 借款项目模型
class Loan(BaseModel):
    # 项目名称
    name = models.CharField(max_length=20, verbose_name='项目名称')
    # 贷款金额 整数
    amount = models.PositiveIntegerField(verbose_name='贷款金额')
    # 贷款期限
    term = models.PositiveIntegerField(verbose_name='贷款期限', null=True, blank=True)
    # 到期时间
    due_date = models.DateField(verbose_name='到期时间', null=True, blank=True)
    # 贷款利率(年化)
    rate = models.FloatField(verbose_name='贷款利率(年化)')
    # 利率类型
    rate_type = models.CharField(
        max_length=10, 
        choices=[
            ('year', '年化利率'),
            ('month', '月利率'),
            ('day', '日利率'),
            ('none', '无利息')
        ],
        default='year',
        verbose_name='利率类型'
    )
    # 利息总额
    interest_sum = models.FloatField(verbose_name='利息总额', default=0)
    # 贷款��期
    loan_date = models.DateField(verbose_name='起息日期')
    # 还款方式
    repayment_method = models.IntegerField(choices=[(1, '等额本息'), (2, '等额本金'), (3, '先息后本'), (4, '一次性还本付息')],
                                        verbose_name='还款方式')
    # 服务费
    service_charge = models.PositiveIntegerField(null=True, blank=True, verbose_name='服务费')
    # 渠道 外键 ,删除渠道时,借款项目不删除,多对一
    channel = models.ForeignKey(Channel, on_delete=models.DO_NOTHING, verbose_name='渠道')
    # 备注
    remark = models.TextField(null=True, blank=True, verbose_name='备注')
    # 所属用户
    member = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='所属用户')
    # 还款状态
    status = models.BooleanField(default=False, verbose_name='还款状态')
    # 是否提前还款
    is_early_repayment = models.BooleanField(default=False, verbose_name='是否提前还款')
    # 提前还款类型
    early_repayment_type = models.CharField(
        max_length=10,
        choices=[
            ('full', '全部还款'),
            ('partial', '部分还款')
        ],
        null=True,
        blank=True,
        verbose_name='提前还款类型'
    )
    # 提前还款日期
    early_repayment_date = models.DateField(null=True, blank=True, verbose_name='提前还款日期')
    # 提前还款金额
    early_repayment_amount = models.FloatField(null=True, blank=True, verbose_name='提前还款金额')
    # 提前还款节省利息
    early_repayment_saved_interest = models.FloatField(null=True, blank=True, verbose_name='提前还款节省利息')


    class Meta:
        verbose_name = '借款项目'
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.name


# 还款计划模型
class Repayment(BaseModel):
    # 贷款项目
    loan = models.ForeignKey(Loan, on_delete=models.CASCADE, verbose_name="贷款项目", related_name='repayments')
    # 本金
    capital = models.FloatField(verbose_name="本金")
    # 利息
    interest = models.FloatField(verbose_name="利息")
    # 本息合计
    total_capital_interest = models.FloatField(verbose_name="本息合计")
    # 剩余未还本金
    remain_capital = models.FloatField(verbose_name="剩余未还本金")
    # 累计利息
    total_interest = models.FloatField(verbose_name="累计利息")
    # 还款日期
    repayment_date = models.DateField(verbose_name="还款日期")
    # 当前期数
    current_period = models.IntegerField(verbose_name="当前期数")
    # 状态
    status = models.BooleanField(default=False, verbose_name="状态")
    # 是否提前还款
    is_early_repayment = models.BooleanField(default=False, verbose_name="是否提前还款")
    # 实际还款日期
    actual_repayment_date = models.DateField(null=True, blank=True, verbose_name="实际还款日期")
    # 实际还款金额
    actual_repayment_amount = models.FloatField(null=True, blank=True, verbose_name="实际还款金额")
    # 节省利息
    saved_interest = models.FloatField(null=True, blank=True, verbose_name="节省利息")


    class Meta:
        verbose_name = '还款计划'
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.loan.name
