from decimal import Decimal, ROUND_HALF_UP
from rest_framework import serializers
from drf_spectacular.utils import extend_schema_field
from drf_spectacular.types import OpenApiTypes
from .models import Loan, Repayment, Channel
from dateutil.relativedelta import relativedelta
from .services import RepaymentService


class RepaymentSerializer(serializers.ModelSerializer):
    """还款记录序列化器"""
    capital = serializers.SerializerMethodField()
    interest = serializers.SerializerMethodField()
    total_capital_interest = serializers.SerializerMethodField() 
    remain_capital = serializers.SerializerMethodField()
    total_interest = serializers.SerializerMethodField()

    class Meta:
        model = Repayment
        fields = [
            "id",
            "current_period",
            "repayment_date",
            "capital",
            "interest",
            "total_capital_interest",
            "remain_capital",
            "status",
            "total_interest",
        ]

    def _format_decimal(self, value):
        """格式化decimal为两位小数"""
        if value is None:
            return "0.00"
        return Decimal(str(value)).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

    def get_capital(self, obj):
        return self._format_decimal(obj.capital)

    def get_interest(self, obj):
        return self._format_decimal(obj.interest)

    def get_total_capital_interest(self, obj):
        return self._format_decimal(obj.total_capital_interest)

    def get_remain_capital(self, obj):
        return self._format_decimal(obj.remain_capital)

    def get_total_interest(self, obj):
        return self._format_decimal(obj.total_interest)


class LoanSerializer(serializers.ModelSerializer):
    channel_icon = serializers.SerializerMethodField()
    remaining_principal = serializers.SerializerMethodField()
    current_period_amount = serializers.SerializerMethodField()
    channel_name = serializers.CharField(source="channel.name", read_only=True)
    next_repayment_date = serializers.DateField(read_only=True)
    days_remaining = serializers.IntegerField(read_only=True)
    current_period = serializers.IntegerField(read_only=True)
    interest_sum = serializers.FloatField(read_only=True)
    status = serializers.BooleanField(read_only=True)
    member = serializers.HiddenField(default=serializers.CurrentUserDefault())
    repayment_method_display = serializers.SerializerMethodField()
    remaining_interest = serializers.SerializerMethodField()
    rate_type_display = serializers.SerializerMethodField()
    repayment_method = serializers.IntegerField()
    term = serializers.IntegerField(required=False, allow_null=True)
    channel_id = serializers.PrimaryKeyRelatedField(
        source='channel',
        queryset=Channel.objects.all()
    )

    class Meta:
        model = Loan
        fields = [
            "id",
            "name",
            "amount",
            "rate",
            "rate_type",
            "rate_type_display",
            "term",
            "due_date",
            "status",
            "create_time",
            "update_time",
            "loan_date",
            "remark",
            "repayment_method",
            "repayment_method_display",  
            "channel_icon",
            "channel_name",
            "channel_id",
            "remaining_principal",
            "remaining_interest",
            "current_period_amount",
            "next_repayment_date",
            "days_remaining",
            "current_period",
            "interest_sum",
            "service_charge",
            "member",
        ]

    def get_rate_type_display(self, obj):
        """获取利率类型的显示值"""
        return obj.get_rate_type_display()

    def validate(self, data):
        """
        校验数据
        """
        # 校验利率
        rate = data.get('rate')
        rate_type = data.get('rate_type', 'year')  # 默认年化利率
        repayment_method = data.get('repayment_method')
        loan_date = data.get('loan_date')
        due_date = data.get('due_date')
        
        if rate < 0:
            raise serializers.ValidationError({"rate": "借款利率不能为负数"})
        
        # 根据利率类型设置不同的验证规则
        if rate_type == 'year':
            pass  # 不限制年化利率上限
        elif rate_type == 'month':
            # 转换为年化利率
            data['rate'] = rate * 12
        elif rate_type == 'day':
            # 转换为年化利率
            data['rate'] = Decimal(str(rate)) * Decimal('365')
        elif rate_type == 'none':
            if rate != 0:
                raise serializers.ValidationError({"rate": "无利息情况下利率必须为0"})
            
        # 除了一次性还本付息和无利息外，其他还款方式的利率必须大于0
        if rate == 0 and rate_type != 'none' and repayment_method != 4:
            raise serializers.ValidationError({"rate": "除一次性还本付息和无利息，借款利率必须大于0"})

        # 根据还款方式验证期限和到期时间
        if repayment_method == 4:  # 一次性还本付息
            if not due_date:
                raise serializers.ValidationError({"due_date": "一次性还本付息必须设置到期时间"})
            if due_date <= loan_date:
                raise serializers.ValidationError({"due_date": "到期时间必须在起息日期之后"})
            # 计算实际天数
            days = (due_date - loan_date).days
            if days < 1:
                raise serializers.ValidationError({"due_date": "借款期限不能小于1天"})
            if days > 365:
                raise serializers.ValidationError({"due_date": "借款期限不能超过365天"})
            # 设置term为1，用于兼容其他逻辑
            data['term'] = 1
        else:
            if not data.get('term'):
                raise serializers.ValidationError({"term": "请输入借款期限"})
            term = data.get('term')
            if term < 1:
                raise serializers.ValidationError({"term": "借款期限不能小于1期"})
            if term > 360:
                raise serializers.ValidationError({"term": "借款期限不能超过360期"})

        return data

    def create(self, validated_data):
        """创建贷款记录"""
        return RepaymentService.create_loan_with_repayments(validated_data)

    def update(self, instance, validated_data):
        """更新贷款记录"""
        return RepaymentService.update_loan_with_repayments(instance, validated_data)

    @extend_schema_field(OpenApiTypes.STR)
    def get_channel_icon(self, obj) -> str:
        if obj.channel and obj.channel.icon:
            return f"https://media.suiqianji.mircool.cn/{obj.channel.icon}"
        return None

    def _format_decimal(self, value):
        """格式化decimal为两位小数"""
        if value is None:
            return "0.00"
        return Decimal(str(value)).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

    @extend_schema_field(OpenApiTypes.DECIMAL)
    def get_remaining_principal(self, obj) -> str:
        """获取待还本金"""
        latest_repayment = (
            Repayment.objects.filter(loan=obj, status=False)
            .order_by("current_period")
            .first()
        )
        value = latest_repayment.remain_capital if latest_repayment else Decimal("0.00")
        return self._format_decimal(value)

    @extend_schema_field(OpenApiTypes.DECIMAL)
    def get_current_period_amount(self, obj) -> str:
        """获取本期应还金额"""
        current_repayment = (
            Repayment.objects.filter(loan=obj, status=False)
            .order_by("current_period")
            .first()
        )
        value = current_repayment.total_capital_interest if current_repayment else Decimal("0.00")
        return self._format_decimal(value)

    @extend_schema_field(OpenApiTypes.DECIMAL)
    def get_remaining_interest(self, obj) -> str:
        """获取待还利息总额"""
        from django.db.models import Sum
        remaining_interest = (
            obj.repayments.filter(status=False)
            .aggregate(total=Sum('interest'))
            .get('total', 0)
        )
        return self._format_decimal(remaining_interest)

    @extend_schema_field(OpenApiTypes.STR)
    def get_repayment_method_display(self, obj):
        """获取还款方式的显示值"""
        method_map = {
            "1": "等额本息",
            "2": "等额本金",
            "3": "先息后本",
            "4": "一次性还本付息"
        }
        return method_map.get(str(obj.repayment_method), "未知")


class RepaymentRepaySerializer(serializers.ModelSerializer):
    """还款计划还款序列化器"""

    class Meta:
        model = Repayment
        fields = ["id"]

    def update(self, instance, validated_data):
        try:
            return RepaymentService.process_repayment(instance.id)
        except ValueError as e:
            raise serializers.ValidationError(str(e))


class EarlyRepaymentSerializer(serializers.Serializer):
    """提前还款序列化器"""
    repayment_type = serializers.ChoiceField(
        choices=[("full", "全部还款"), ("partial", "部分还款")],
        required=True,
        error_messages={"required": "请选择还款类型"}
    )
    amount = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        required=False,
        error_messages={
            "invalid": "金额格式不正确",
            "max_digits": "金额不能超过8位数",
            "decimal_places": "金额只能保留两位小数"
        }
    )

    def validate(self, data):
        """验证数据"""
        repayment_type = data.get("repayment_type")
        amount = data.get("amount")

        if repayment_type == "partial" and not amount:
            raise serializers.ValidationError("部分还款时必须指定还款金额")
        
        if amount and amount <= 0:
            raise serializers.ValidationError("还款金额必须大于0")

        return data
