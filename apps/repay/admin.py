from django.contrib import admin

from apps.repay.models import Loan, Repayment


@admin.register(Loan)
class LoanAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'name', 'amount', 'term', 'rate', 'interest_sum', 'loan_date', 'channel', 'get_nickname', 'get_mobile', 'status',
        'create_time')
    list_display_links = ('id', 'name')
    list_per_page = 25

    def get_nickname(self, obj):
        return obj.member.nickname if obj.member else '-'
    get_nickname.short_description = '昵称'

    def get_mobile(self, obj):
        return obj.member.mobile if obj.member else '-'
    get_mobile.short_description = '手机号'

    # 搜索框查询
    # search_fields = ['name']
    # search_help_text = "支持模糊查询"

    # 设置禁止编辑
    def get_readonly_fields(self, request, obj=None):
        # 如果 obj 存在（编辑页面），则设置特定字段为只读
        if obj:
            return [f.name for f in self.model._meta.fields]
        return []  # 在新增页面，没有字段设置为只读


@admin.register(Repayment)
class RepaymentAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'loan', 'current_period', 'status', 'capital', 'interest', 'total_capital_interest', 'remain_capital',
        'total_interest', 'repayment_date', 'update_time')
    list_display_links = ('id', 'loan')
    list_per_page = 20
    # 排序id正序
    ordering = ('id',)

    # 设置禁止编辑
    def get_readonly_fields(self, request, obj=None):
        # 如果 obj 存在（编辑页面），则设置特定字段为只读
        if obj:
            return [f.name for f in self.model._meta.fields]
        return []
