from datetime import date

from celery import shared_task
from django.db.models import Q

from apps.repay.models import Repayment
from apps.member.models import SMSRecord
from utils.sms import sms_service


@shared_task
def send_repayment_reminder():
    """发送还款提醒短信"""
    today = date.today()
    
    # 获取今天需要还款的记录
    repayments = Repayment.objects.filter(
        Q(repayment_date=today) &  # 今天需要还款
        Q(status=False) &  # 未还款
        Q(loan__status=False) &  # 贷款未结清
        Q(loan__member__mobile__isnull=False)  # 用户有手机号
    ).select_related('loan', 'loan__member')
    
    for repayment in repayments:
        # 检查是否已发送过提醒
        if SMSRecord.objects.filter(
            repayment_id=repayment.id,
            create_time__date=today,
            sms_type='repayment'
        ).exists():
            continue
            
        # 准备短信参数
        params = {
            'name': repayment.loan.name,
            'amount': str(repayment.total_capital_interest),
            'date': today.strftime('%Y-%m-%d')
        }
        
        # 发送短信
        sms_service.send_repayment_reminder(
            member=repayment.loan.member,
            phone=repayment.loan.member.mobile,
            params=params,
            repayment_id=repayment.id
        ) 