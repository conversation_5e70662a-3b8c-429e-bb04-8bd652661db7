from django_filters import rest_framework as filters
from django.db.models import Count, F, IntegerField, Min, Q, Value
from django.utils import timezone
from .models import Loan

class LoanFilter(filters.FilterSet):
    """贷款过滤器类
    
    用于处理贷款查询的过滤条件,支持按状态、渠道、放款日期等条件过滤
    同时会为查询结果添加下次还款日期、当前期数等注解信息
    """
    
    # 还款状态过滤字段 - True表示已还清,False表示未还清
    status = filters.BooleanFilter(help_text="还款状态(True:已还清,False:未还清)")
    
    # 渠道ID过滤字段 - 按贷款所属渠道筛选
    channel = filters.NumberFilter(field_name="channel_id", help_text="渠道ID")
    
    # 放款日期范围过滤 - 起始日期(大于等于)
    loan_date_after = filters.DateFilter(
        field_name="loan_date", 
        lookup_expr="gte", 
        help_text="起始放款日期"
    )
    
    # 放款日期范围过滤 - 结束日期(小于等于) 
    loan_date_before = filters.DateFilter(
        field_name="loan_date", 
        lookup_expr="lte", 
        help_text="结束放款日期"
    )
    
    class Meta:
        model = Loan
        fields = ["status", "channel"]

    def filter_queryset(self, queryset):
        """处理查询集的过滤和注解
        
        主要处理:
        1. 执行父类的过滤方法,应用基础过滤条件
        2. 添加next_repayment_date(下次还款日期)注解:
           - 查找未还款记录中最早的还款日期
        3. 添加current_period(当前期数)注解:
           - 统计已还款记录数量+1
        4. 计算days_remaining(距下次还款剩余天数):
           - 下次还款日期与当前日期的差值
        5. 按创建时间倒序排序
        
        Returns:
            QuerySet: 处理后的查询集,包含注解字段
        """
        # 执行父类的过滤逻辑
        queryset = super().filter_queryset(queryset)
        
        # 获取当前日期,用于计算剩余天数
        today = timezone.now().date()
        
        # 添加关联查询和注解字段
        queryset = queryset.select_related("channel").annotate(
            # 注解:下次还款日期(未还款记录中最早的还款日期)
            next_repayment_date=Min(
                "repayments__repayment_date", 
                filter=Q(repayments__status=False)
            ),
            # 注解:当前期数(已还款记录数量+1)
            current_period=Count(
                "repayments", 
                filter=Q(repayments__status=True)
            ) + 1,
        ).order_by("-create_time")  # 按创建时间倒序

        # 为每条记录计算距离下次还款的剩余天数
        for loan in queryset:
            if loan.next_repayment_date:
                # 如果有下次还款日期,计算与当前日期的差值
                loan.days_remaining = (loan.next_repayment_date - today).days
            else:
                # 如果没有下次还款日期(已结清),剩余天数为None
                loan.days_remaining = None

        return queryset 