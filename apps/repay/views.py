from decimal import ROUND_HALF_UP, Decimal

from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import (
    OpenApiExample,
    OpenApiParameter,
    extend_schema,
    inline_serializer,
)
from rest_framework import mixins, serializers, status, viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend

from .models import Loan, Repayment
from .serializers import LoanSerializer, RepaymentRepaySerializer, RepaymentSerializer, EarlyRepaymentSerializer
from .services import RepaymentService
from .filters import LoanFilter


class LoanViewSet(viewsets.ModelViewSet):
    """贷款视图集，提供贷款记录的增删改查功能"""

    permission_classes = [IsAuthenticated]
    serializer_class = LoanSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_class = LoanFilter

    def get_queryset(self):
        """返回贷款记录查询集
        - 列表查询：仅返回未还清的贷款记录
        - 详情查询：返回所有状态的贷款记录
        """
        queryset = Loan.objects.filter(member=self.request.user)
        
        # 如果是列表查询且没有显式指定status参数，只返回未还清的贷款
        if self.action == "list" and "status" not in self.request.query_params:
            queryset = queryset.filter(status=False)
            
        return queryset

    @extend_schema(
        summary="获取还款统计数据",
        description="""统计用户的还款数据，包括：
                    - 近30日待还金额
                    - 全部待还本金
                    - 全部待还利息
                    - 待还总额（本金+利息）""",
        responses={
            200: {
                "type": "object",
                "properties": {
                    "next_month_amount": {
                        "type": "string",
                        "format": "decimal",
                        "example": "1000.00",
                    },
                    "total_principal": {
                        "type": "string",
                        "format": "decimal",
                        "example": "5000.00",
                    },
                    "total_interest": {
                        "type": "string",
                        "format": "decimal",
                        "example": "250.00",
                    },
                    "total_amount": {
                        "type": "string",
                        "format": "decimal",
                        "example": "5250.00",
                    },
                },
                "description": "所有金额字段均保留两位小数",
            }
        },
        tags=["贷款管理"],
    )
    @action(detail=False, methods=["get"])
    def get_statistics(self, request):
        """获取还款统计数据"""
        return Response(
            RepaymentService.get_loan_statistics(request.user)
        )

    @extend_schema(
        summary="获取还款计划",
        description="获取指定贷款项目的还款计划列表，按还款日期排序。注意：用户只能查询自己创建的贷款项目的还款计划。",
        parameters=[
            OpenApiParameter(
                name="id",
                location=OpenApiParameter.PATH,
                description="贷款项目ID（必须是当前用户创建的项目）",
                type=int,
                required=True,
            )
        ],
        responses={
            200: RepaymentSerializer(many=True),
            404: OpenApiExample(
                "Project Not Found",
                value={"code": 404, "message": "项目不存在或无权限访问"},
            ),
        },
    )
    @action(detail=True, methods=["get"])
    def repayment(self, request, pk=None):
        try:
            loan = self.get_object()
            repayments = Repayment.objects.filter(loan=loan).order_by("repayment_date")
            serializer = RepaymentSerializer(repayments, many=True)
            return Response(serializer.data)
        except Loan.DoesNotExist:
            return Response(
                {"code": 404, "message": "项目不存在或无权限访问"},
                status=status.HTTP_404_NOT_FOUND,
            )

    @extend_schema(
        summary="更新贷款信息",
        description="更新贷款基本信息并重新计算还款计划",
        responses={
            200: LoanSerializer,
            400: OpenApiExample(
                "Bad Request", value={"detail": "更新失败,请检查输入数据"}
            ),
            404: OpenApiExample(
                "Not Found", value={"detail": "贷款记录不存在或无权访问"}
            ),
        },
        tags=["贷款管理"],
    )
    def update(self, request, *args, **kwargs):
        """更新贷款信息"""
        try:
            instance = self.get_object()

            # 检查是否有已还款的记录
            if instance.repayments.filter(status=True).exists():
                raise serializers.ValidationError("已有还款记录,无法修改贷款信息")

            # 更新贷款信息
            serializer = self.get_serializer(instance, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)

            # 开启事务
            from django.db import transaction

            with transaction.atomic():
                # 保存更新后的贷款信息
                self.perform_update(serializer)

            return Response(serializer.data)

        except Loan.DoesNotExist:
            return Response(
                {"detail": "贷款记录不存在或无权访问"}, status=status.HTTP_404_NOT_FOUND
            )
        except serializers.ValidationError as e:
            return Response(
                {
                    "detail": (
                        str(e.detail[0])
                        if isinstance(e.detail, list)
                        else str(e.detail)
                    )
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return Response(
                {"detail": f"更新失败: {str(e)}"}, status=status.HTTP_400_BAD_REQUEST
            )

    @extend_schema(
        summary="提前还款",
        description="提供全部还款和部分还款两种方式",
        request=EarlyRepaymentSerializer,
        responses={
            200: OpenApiExample(
                "Success",
                value={
                    "message": "还款成功",
                    "repaid_amount": 1000.00,
                    "repaid_periods": [1, 2]  # 仅部分还款时返回
                }
            ),
            400: OpenApiExample(
                "Bad Request",
                value={"message": "还款失败,请检查输入数据"}
            ),
            404: OpenApiExample(
                "Not Found",
                value={"message": "贷款记录不存在或无权访问"}
            ),
        },
        tags=["贷款管理"],
    )
    @action(detail=True, methods=["post"])
    def early_repayment(self, request, pk=None):
        """提前还款"""
        try:
            loan = self.get_object()
            serializer = EarlyRepaymentSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            
            result = RepaymentService.early_repayment(
                loan_id=loan.id,
                repayment_type=serializer.validated_data["repayment_type"],
                amount=serializer.validated_data.get("amount")
            )
            
            return Response(result)
            
        except Loan.DoesNotExist:
            return Response(
                {"message": "贷款记录不存在或无权访问"},
                status=status.HTTP_404_NOT_FOUND
            )
        except serializers.ValidationError as e:
            return Response(
                {"message": str(e.detail[0]) if isinstance(e.detail, list) else str(e.detail)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except ValueError as e:
            return Response(
                {"message": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {"message": f"还款失败: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class RepaymentViewSet(mixins.UpdateModelMixin, viewsets.GenericViewSet):
    """还款计划视图集"""

    permission_classes = [IsAuthenticated]
    serializer_class = RepaymentRepaySerializer
    http_method_names = ["patch"]  # 只允许 patch 方法

    def get_queryset(self):
        return Repayment.objects.filter(loan__member=self.request.user)

    @extend_schema(
        summary="执行还款",
        description="将指定的还款计划标记为已还款。如果该贷款的所有期数都已还款，贷款状态将更新为已结清。",
        responses={
            200: OpenApiExample("Success", value={"message": "还款成功"}),
            404: OpenApiExample(
                "Not Found", value={"code": 404, "message": "还款计划不存在或无权访问"}
            ),
            400: OpenApiExample(
                "Bad Request", value={"code": 400, "message": "该期已经还款"}
            ),
        },
        tags=["还款管理"],
    )
    def partial_update(self, request, *args, **kwargs):
        """还款接口"""
        try:
            repayment = self.get_object()
            serializer = self.get_serializer(repayment, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response({"message": "还款成功"})
        except Repayment.DoesNotExist:
            return Response(
                {"code": 404, "message": "还款计划不存在或无权访问"},
                status=status.HTTP_404_NOT_FOUND,
            )
        except serializers.ValidationError as e:
            return Response(
                {"code": 400, "message": str(e.detail[0])},
                status=status.HTTP_400_BAD_REQUEST,
            )

    def create(self, request, *args, **kwargs):
        try:
            return super().create(request, *args, **kwargs)
        except serializers.ValidationError as e:
            # 格式化错误信息
            error_messages = []
            for field, errors in e.detail.items():
                if isinstance(errors, list):
                    error_messages.extend(errors)
                else:
                    error_messages.append(str(errors))

            return Response(
                {"code": 400, "message": " ".join(error_messages)},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return Response(
                {"code": 500, "message": "添加失败，请稍后重试"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
