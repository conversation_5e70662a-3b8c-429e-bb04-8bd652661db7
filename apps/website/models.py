from django.db import models
from config.models import BaseModel
from django.utils.text import slugify
import uuid

# 平台协议
class Protocol(BaseModel):
    title = models.CharField(max_length=255, verbose_name="标题")
    content = models.TextField(verbose_name="协议内容")
    status = models.BooleanField(default=True, verbose_name="状态")
    slug = models.SlugField(max_length=255, unique=True, blank=True, null=True, verbose_name="唯一标识",
                          help_text="可选。用于URL的唯一标识，如未提供将自动根据标题生成。")

    class Meta:
        verbose_name = "平台协议"
        verbose_name_plural = verbose_name
        ordering = ["-create_time"]

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        if not self.slug and self.title:
            # 如果没有提供slug，则使用title生成
            self.slug = slugify(self.title)
            
            # 如果生成的slug已存在，则在后面添加数字
            original_slug = self.slug
            counter = 1
            while Protocol.objects.filter(slug=self.slug).exists():
                self.slug = f"{original_slug}-{counter}"
                counter += 1
                
        super().save(*args, **kwargs)
