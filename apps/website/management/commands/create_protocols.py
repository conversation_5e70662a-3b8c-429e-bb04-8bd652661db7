from django.core.management.base import BaseCommand
from apps.website.models import Protocol
import os

class Command(BaseCommand):
    help = '创建用户协议和隐私政策'

    def handle(self, *args, **options):
        # 读取用户协议
        user_agreement_path = os.path.join('apps', 'website', 'templates', 'user_agreement.txt')
        with open(user_agreement_path, 'r', encoding='utf-8') as f:
            user_agreement_content = f.read()

        # 读取隐私政策
        privacy_policy_path = os.path.join('apps', 'website', 'templates', 'privacy_policy.txt')
        with open(privacy_policy_path, 'r', encoding='utf-8') as f:
            privacy_policy_content = f.read()

        # 创建用户协议
        Protocol.objects.create(
            title='用户服务协议',
            content=user_agreement_content,
            status=True
        )
        self.stdout.write(self.style.SUCCESS('成功创建用户服务协议'))

        # 创建隐私政策
        Protocol.objects.create(
            title='隐私政策',
            content=privacy_policy_content,
            status=True
        )
        self.stdout.write(self.style.SUCCESS('成功创建隐私政策'))
