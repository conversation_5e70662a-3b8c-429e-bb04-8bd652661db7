from django.shortcuts import render, get_object_or_404
from rest_framework import viewsets, permissions, mixins
from rest_framework.viewsets import GenericViewSet
from rest_framework.decorators import action
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiResponse
from .models import Protocol
from .serializers import ProtocolSerializer

# Create your views here.

class ProtocolViewSet(mixins.RetrieveModelMixin, GenericViewSet):
    """平台协议视图集，提供协议查询功能"""
    queryset = Protocol.objects.all()
    serializer_class = ProtocolSerializer
    permission_classes = [permissions.AllowAny]
    lookup_field = 'pk'  # 默认使用 pk 查询

    @extend_schema(
        summary="获取协议详情",
        description="通过ID获取单个平台协议的详细信息",
        parameters=[
            OpenApiParameter(
                name="id",
                location=OpenApiParameter.PATH,
                description="协议ID",
                required=True,
                type=int
            ),
        ],
        responses={
            200: OpenApiResponse(
                response=ProtocolSerializer,
                description="成功获取协议信息"
            ),
            404: OpenApiResponse(
                description="协议不存在"
            )
        },
        tags=["平台协议"]
    )
    def retrieve(self, request, *args, **kwargs):
        """获取单个协议详情"""
        return super().retrieve(request, *args, **kwargs)

    @extend_schema(
        summary="通过Slug获取协议详情",
        description="通过唯一标识(slug)获取单个平台协议的详细信息",
        parameters=[
            OpenApiParameter(
                name="slug",
                location=OpenApiParameter.PATH,
                description="协议唯一标识",
                required=True,
                type=str
            ),
        ],
        responses={
            200: OpenApiResponse(
                response=ProtocolSerializer,
                description="成功获取协议信息"
            ),
            404: OpenApiResponse(
                description="协议不存在"
            )
        },
        tags=["平台协议"]
    )
    @action(detail=False, methods=['get'], url_path='by-slug/(?P<slug>[-\w]+)')
    def get_by_slug(self, request, slug=None):
        """通过slug获取协议详情"""
        protocol = get_object_or_404(Protocol, slug=slug)
        serializer = self.get_serializer(protocol)
        return Response(serializer.data)
