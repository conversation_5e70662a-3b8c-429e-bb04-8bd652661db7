# Generated by Django 5.1.3 on 2024-11-26 08:48

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Protocol",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                ("title", models.CharField(max_length=255, verbose_name="标题")),
                ("content", models.TextField(verbose_name="协议内容")),
                ("status", models.BooleanField(default=True, verbose_name="状态")),
            ],
            options={
                "verbose_name": "平台协议",
                "verbose_name_plural": "平台协议",
                "ordering": ["-create_time"],
            },
        ),
    ]
