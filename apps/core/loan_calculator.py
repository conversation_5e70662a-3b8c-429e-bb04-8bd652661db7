"""
贷款计算工具库
提供等额本息、等额本金等贷款计算功能
"""
from decimal import Decimal, ROUND_HALF_UP
from typing import List, Dict, Tuple
from dataclasses import dataclass


@dataclass
class LoanPayment:
    """贷款还款信息"""
    period: int  # 期数
    total_payment: Decimal  # 总还款
    principal: Decimal  # 本金
    interest: Decimal  # 利息
    remaining_principal: Decimal  # 剩余本金


class LoanCalculator:
    def __init__(self, principal: float, annual_rate: float, months: int):
        """
        初始化贷款计算器
        :param principal: 贷款本金
        :param annual_rate: 年利率(%)
        :param months: 贷款期数(月)
        """
        self.principal = Decimal(str(principal))
        self.monthly_rate = Decimal(str(annual_rate / 12 / 100))
        self.months = months

    def calculate_equal_installment(self) -> List[LoanPayment]:
        """
        计算等额本息还款
        每月还款金额相同，其中本金逐月递增，利息逐月递减
        :return: 每期还款详情列表
        """
        # 计算每月还款金额
        monthly_payment = self.principal * (
            self.monthly_rate * (1 + self.monthly_rate) ** self.months
        ) / ((1 + self.monthly_rate) ** self.months - 1)
        
        # 保留2位小数
        monthly_payment = monthly_payment.quantize(Decimal('0.01'), ROUND_HALF_UP)
        
        payments = []
        remaining_principal = self.principal
        
        for period in range(1, self.months + 1):
            # 计算每月利息
            interest = remaining_principal * self.monthly_rate
            interest = interest.quantize(Decimal('0.01'), ROUND_HALF_UP)
            
            # 计算每月本金
            principal = monthly_payment - interest
            principal = principal.quantize(Decimal('0.01'), ROUND_HALF_UP)
            
            # 最后一期需要处理误差
            if period == self.months:
                principal = remaining_principal
                monthly_payment = principal + interest
            
            # 更新剩余本金
            remaining_principal -= principal
            remaining_principal = remaining_principal.quantize(Decimal('0.01'), ROUND_HALF_UP)
            
            payment = LoanPayment(
                period=period,
                total_payment=monthly_payment,
                principal=principal,
                interest=interest,
                remaining_principal=remaining_principal
            )
            payments.append(payment)
            
        return payments

    def calculate_equal_principal(self) -> List[LoanPayment]:
        """
        计算等额本金还款
        每月归还等额的本金和剩余贷款在该月产生的利息
        :return: 每期还款详情列表
        """
        # 每月应还本金
        monthly_principal = self.principal / Decimal(str(self.months))
        monthly_principal = monthly_principal.quantize(Decimal('0.01'), ROUND_HALF_UP)
        
        payments = []
        remaining_principal = self.principal
        
        for period in range(1, self.months + 1):
            # 最后一期处理本金误差
            if period == self.months:
                monthly_principal = remaining_principal
                
            # 计算每月利息
            interest = remaining_principal * self.monthly_rate
            interest = interest.quantize(Decimal('0.01'), ROUND_HALF_UP)
            
            # 计算每月总还款
            total_payment = monthly_principal + interest
            total_payment = total_payment.quantize(Decimal('0.01'), ROUND_HALF_UP)
            
            # 更新剩余本金
            remaining_principal -= monthly_principal
            remaining_principal = remaining_principal.quantize(Decimal('0.01'), ROUND_HALF_UP)
            
            payment = LoanPayment(
                period=period,
                total_payment=total_payment,
                principal=monthly_principal,
                interest=interest,
                remaining_principal=remaining_principal
            )
            payments.append(payment)
            
        return payments


def calculate_loan(principal: float, annual_rate: float, months: int, payment_type: str = "等额本息") -> List[Dict]:
    """
    计算贷款还款计划
    :param principal: 贷款本金
    :param annual_rate: 年利率(%)
    :param months: 贷款期数(月)
    :param payment_type: 还款方式 ("等额本息" 或 "等额本金")
    :return: 还款计划列表
    """
    calculator = LoanCalculator(principal, annual_rate, months)
    
    if payment_type == "等额本息":
        payments = calculator.calculate_equal_installment()
    elif payment_type == "等额本金":
        payments = calculator.calculate_equal_principal()
    else:
        raise ValueError("不支持的还款方式")
        
    # 转换为字典列表
    return [
        {
            "period": payment.period,
            "total_payment": float(payment.total_payment),
            "principal": float(payment.principal),
            "interest": float(payment.interest),
            "remaining_principal": float(payment.remaining_principal)
        }
        for payment in payments
    ]
