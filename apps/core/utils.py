from rest_framework.views import exception_handler
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken

def custom_exception_handler(exc, context):
    """
    自定义异常处理器，简化令牌错误响应
    """
    response = exception_handler(exc, context)
    
    if response is not None:
        # 处理令牌相关错误
        if isinstance(exc, (TokenError, InvalidToken)):
            response.data = {
                'code': 'token_not_valid',
                'detail': '令牌无效或已过期'
            }
            
    return response
