# Generated by Django 5.1.3 on 2024-12-01 21:05

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("member", "0031_rename_updated_time_member_update_time"),
    ]

    operations = [
        migrations.CreateModel(
            name="SMSRecord",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                ("phone", models.CharField(max_length=11, verbose_name="手机号码")),
                ("content", models.TextField(verbose_name="短信内容")),
                ("status", models.BooleanField(default=True, verbose_name="发送状态")),
                (
                    "error_message",
                    models.TextField(blank=True, null=True, verbose_name="错误信息"),
                ),
                (
                    "repayment_id",
                    models.IntegerField(
                        blank=True, null=True, verbose_name="还款计划ID"
                    ),
                ),
                (
                    "sms_type",
                    models.CharField(
                        choices=[
                            ("repayment", "还款提醒"),
                            ("overdue", "逾期提醒"),
                            ("other", "其他"),
                        ],
                        default="repayment",
                        max_length=20,
                        verbose_name="短信类型",
                    ),
                ),
                (
                    "member",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "短信记录",
                "verbose_name_plural": "短信记录",
                "db_table": "member_sms_record",
                "ordering": ["-create_time"],
            },
        ),
    ]
