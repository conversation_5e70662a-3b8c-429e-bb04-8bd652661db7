# Generated by Django 4.2.13 on 2024-11-24 16:39

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("member", "0029_update_member_fields"),
    ]

    operations = [
        migrations.AddField(
            model_name="member",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                default=django.utils.timezone.now,
                verbose_name="创建时间",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="member",
            name="updated_time",
            field=models.DateTimeField(auto_now=True, verbose_name="更新时间"),
        ),
    ]
