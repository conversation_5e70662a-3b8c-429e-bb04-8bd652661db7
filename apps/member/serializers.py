from rest_framework import serializers

from .models import Member


class MemberSerializer(serializers.ModelSerializer):
    class Meta:
        model = Member
        fields = ('id', 'username', 'nickname', 'avatar', 'openid', 'mobile')
        extra_kwargs = {
            'password': {'write_only': True},
            'openid': {'read_only': True},
            'mobile': {'read_only': True}
        }


class RegisterSerializer(serializers.Serializer):
    username = serializers.CharField(min_length=6, max_length=20)
    password = serializers.CharField(min_length=6, max_length=20, write_only=True)


class LoginSerializer(serializers.Serializer):
    username = serializers.Char<PERSON>ield(min_length=6, max_length=20)
    password = serializers.Char<PERSON>ield(min_length=6, max_length=20, write_only=True)


class UpdateNicknameSerializer(serializers.Serializer):
    nickname = serializers.CharField(min_length=2, max_length=20)


class WeChatLoginSerializer(serializers.Serializer):
    code = serializers.Char<PERSON>ield(min_length=2, max_length=64)


class PhoneLoginSerializer(serializers.Serializer):
    code = serializers.Char<PERSON>ield(required=True, help_text='获取手机号的code')
    js_code = serializers.CharField(required=True, help_text='登录凭证code')


class TokenResponse(serializers.Serializer):
    """登录返回数据序列化器"""
    code = serializers.IntegerField(help_text="状态码")
    message = serializers.CharField(help_text="提示信息")
    data = serializers.DictField(help_text="返回数据")

    class Meta:
        swagger_schema_fields = {
            "example": {
                "code": 200,
                "message": "登录成功",
                "data": {
                    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                    "user": {
                        "id": 1,
                        "username": "user_12345678",
                        "nickname": "",
                        "avatar": "",
                        "mobile": "13800138000",
                        "openid": "xxxxxx"
                    }
                }
            }
        }


class CustomUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = Member
        fields = ('id', 'username', 'nickname', 'avatar', 'mobile')
        extra_kwargs = {
            'password': {'write_only': True},
            'mobile': {'read_only': True}
        }
