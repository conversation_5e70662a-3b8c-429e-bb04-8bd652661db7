from django.contrib import admin
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from .models import Member, SMSRecord


class MemberAdmin(admin.ModelAdmin):
    list_display = ['id', 'username', 'nickname', 'mobile', 'is_active','create_time' ]
    search_fields = ['username', 'nickname', 'mobile']
    list_editable = ['is_active', ] # 可编辑字段
    list_per_page = 20 # 每页显示条数
    actions_on_top = True   # 顶部操作栏
    actions_on_bottom = True    # 底部操作栏
    actions_selection_counter = True    # 选中项数量


@admin.register(SMSRecord)
class SMSRecordAdmin(admin.ModelAdmin):
    """短信记录后台管理"""
    list_display = ['id', 'member_info', 'phone', 'sms_type', 'content_preview', 'status_tag', 'error_preview', 'create_time']
    search_fields = ['phone', 'content', 'error_message', 'member__username', 'member__nickname', 'member__mobile']
    readonly_fields = ['create_time', 'update_time', 'content_full', 'error_full']
    list_per_page = 20
    
    def member_info(self, obj):
        """显示用户信息"""
        if obj.member:
            return format_html(
                '<span title="ID: {}">{} ({})</span>',
                obj.member.id,
                obj.member.username,
                obj.member.nickname or '无昵称'
            )
        return '-'
    member_info.short_description = '用户信息'
    
    def content_preview(self, obj):
        """短信内容预览"""
        if len(obj.content) > 50:
            return format_html(
                '<span title="{}">{}</span>',
                obj.content,
                obj.content[:50] + '...'
            )
        return obj.content
    content_preview.short_description = '短信内容'
    
    def error_preview(self, obj):
        """错误信息预览"""
        if not obj.error_message:
            return '-'
        if len(obj.error_message) > 30:
            return format_html(
                '<span title="{}" style="color: red;">{}</span>',
                obj.error_message,
                obj.error_message[:30] + '...'
            )
        return format_html('<span style="color: red;">{}</span>', obj.error_message)
    error_preview.short_description = '错误信息'
    
    def content_full(self, obj):
        """完整短信内容"""
        return obj.content
    content_full.short_description = '完整短信内容'
    
    def error_full(self, obj):
        """完整错误信息"""
        return obj.error_message or '-'
    error_full.short_description = '完整错误信息'
    
    def status_tag(self, obj):
        """自定义状态显示"""
        if obj.status:
            return format_html(
                '<span style="color: white; background-color: green; padding: 2px 6px; border-radius: 3px;">发送成功</span>'
            )
        return format_html(
            '<span style="color: white; background-color: red; padding: 2px 6px; border-radius: 3px;">发送失败</span>'
        )
    status_tag.short_description = '发送状态'
    
    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('member')
    
    fieldsets = [
        ('基本信息', {
            'fields': ['member', 'phone', 'sms_type']
        }),
        ('发送内容', {
            'fields': ['content_full']
        }),
        ('发送状态', {
            'fields': ['status', 'error_full']
        }),
        ('关联信息', {
            'fields': ['repayment_id']
        }),
        ('时间信息', {
            'fields': ['create_time', 'update_time']
        })
    ]

admin.site.register(Member, MemberAdmin)
