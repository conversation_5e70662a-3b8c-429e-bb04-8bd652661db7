import os
from datetime import datetime

from django.contrib.auth.models import AbstractUser, Group, Permission
from django.core.validators import FileExtensionValidator
from django.db import models

from config.models import BaseModel


def generate_filename(instance, filename):
    now = datetime.now()
    timestamp = now.strftime('%Y%m%d%H%M%S')
    extension = filename.split('.')[-1]
    new_filename = f"{timestamp}.{extension}"
    # avatar/20210901123456.jpg
    return os.path.join('avatar', new_filename)


# 会员表 继承自系统自带的User表
class Member(AbstractUser, BaseModel):
    # 昵称
    nickname = models.CharField(max_length=64, default='微信用户', verbose_name='昵称')
    # 头像 字符串
    avatar = models.ImageField(
        upload_to=generate_filename,
        default='avatar/default.png',
        null=True,
        blank=True,
        validators=[FileExtensionValidator(['jpg', 'jpeg', 'png'])],
        verbose_name='头像')
    # 手机号码
    mobile = models.CharField(
        max_length=11,
        blank=True,
        null=True,
        verbose_name='手机号码',
        db_index=True,  # 添加索引但不是唯一约束
    )
    # openid
    openid = models.CharField(max_length=64, null=True,
                              blank=True, verbose_name='openid')
    # 组
    groups = models.ManyToManyField(Group,
                                    blank=True, related_name='member_groups', verbose_name='组')
    # 权限
    user_permissions = models.ManyToManyField(Permission,
                                              blank=True, related_name='member_permissions', verbose_name='权限')

    class Meta:
        verbose_name = '用户'
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.username


# 短信发送记录模型
class SMSRecord(BaseModel):
    """短信发送记录"""
    member = models.ForeignKey(
        Member,
        on_delete=models.CASCADE,
        verbose_name='用户'
    )
    phone = models.CharField(
        max_length=11,
        verbose_name='手机号码'
    )
    content = models.TextField(
        verbose_name='短信内容'
    )
    status = models.BooleanField(
        default=True,
        verbose_name='发送状态'
    )
    error_message = models.TextField(
        null=True,
        blank=True,
        verbose_name='错误信息'
    )
    # 关联的还款计划ID
    repayment_id = models.IntegerField(
        null=True,
        blank=True,
        verbose_name='还款计划ID'
    )
    # 短信类型
    sms_type = models.CharField(
        max_length=20,
        default='repayment',
        choices=[
            ('repayment', '还款提醒'),
            ('overdue', '逾期提醒'),
            ('other', '其他')
        ],
        verbose_name='短信类型'
    )

    class Meta:
        verbose_name = '短信记录'
        verbose_name_plural = verbose_name
        ordering = ['-create_time']
        db_table = 'member_sms_record'

    def __str__(self):
        return f'{self.phone} - {self.create_time}'
