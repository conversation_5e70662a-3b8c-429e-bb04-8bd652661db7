import logging
import uuid
import httpx
from django.contrib.auth import get_user_model
from rest_framework import permissions, viewsets, status, generics
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.tokens import RefreshToken
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample, OpenApiResponse
from drf_spectacular.types import OpenApiTypes

from config import settings
from .serializers import (
    MemberSerializer,
    LoginSerializer,
    RegisterSerializer,
    PhoneLoginSerializer,
    WeChatLoginSerializer,
    UpdateNicknameSerializer,
    TokenResponse
)

logger = logging.getLogger(__name__)
User = get_user_model()


def code2session(code):
    """
    登录凭证校验。通过 wx.login 接口获得临时登录凭证 code 后传到开发者服务器调用此接口完成登录流程。
    https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-login/code2Session.html
    """
    url = "https://api.weixin.qq.com/sns/jscode2session"
    params = {
        "appid": settings.WECHAT_APPID,
        "secret": settings.WECHAT_APPSECRET,
        "js_code": code,
        "grant_type": "authorization_code"
    }
    response = httpx.get(url, params=params)
    data = response.json()
    logger.info(f"code2session response: {data}")
    
    if "errcode" in data and data["errcode"] != 0:
        error_msg = data.get("errmsg", "登录凭证校验失败")
        raise Exception(error_msg)
    
    return data


def get_access_token():
    """
    获取小程序全局唯一后台接口调用凭据（access_token）。
    调用绝大多数后台接口时都需使用 access_token，开发者需要进行妥善保存。
    """
    url = "https://api.weixin.qq.com/cgi-bin/token"
    params = {
        "grant_type": "client_credential",
        "appid": settings.WECHAT_APPID,
        "secret": settings.WECHAT_APPSECRET
    }
    response = httpx.get(url, params=params)
    data = response.json()
    logger.info(f"get_access_token response: {data}")
    
    if "errcode" in data and data["errcode"] != 0:
        error_msg = data.get("errmsg", "获取access_token失败")
        raise Exception(error_msg)
    
    return data["access_token"]


def get_phone_number(access_token, code):
    """
    获取用户手机号。每个 code 只能使用一次，code的有效期为5分钟。
    https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-info/phone-number/getPhoneNumber.html
    """
    url = f"https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token={access_token}"
    data = {"code": code}
    response = httpx.post(url, json=data)
    result = response.json()
    logger.info(f"get_phone_number response: {result}")
    
    if "errcode" in result and result["errcode"] != 0:
        error_msg = result.get("errmsg", "获取手机号失败")
        raise Exception(error_msg)
    
    return result["phone_info"]


class LoginView(APIView):
    """用户登录视图"""
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="用户登录",
        description="使用用户名和密码进行登录",
        request=LoginSerializer,
        responses={
            200: {
                "description": "登录成功",
                "content": {
                    "application/json": {
                        "example": {
                            "code": 200,
                            "message": "登录成功",
                            "data": {
                                "user": {
                                    "id": 1,
                                    "username": "test",
                                    "nickname": "测试用户",
                                    "avatar": "http://example.com/avatar.jpg",
                                    "mobile": "13800138000"
                                },
                                "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
                            }
                        }
                    }
                }
            },
            400: {
                "description": "登录失败",
                "content": {
                    "application/json": {
                        "example": {
                            "code": 400,
                            "message": "用户名或密码错误"
                        }
                    }
                }
            }
        }
    )
    def post(self, request):
        """用户登录"""
        serializer = LoginSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({"code": 400, "message": "参数错误", "errors": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

        username = serializer.validated_data['username']
        password = serializer.validated_data['password']

        if not User.objects.filter(username=username).exists():
            return Response({"code": 400, "message": "用户名不存在"}, status=status.HTTP_400_BAD_REQUEST)

        user = User.objects.get(username=username)
        if not user.check_password(password):
            return Response({"code": 400, "message": "密码错误"}, status=status.HTTP_400_BAD_REQUEST)

        refresh = RefreshToken.for_user(user)
        return Response({
            "code": 200,
            "message": "登录成功",
            "data": {
                "user": MemberSerializer(user).data,
                "token": str(refresh.access_token)
            }
        })


class RegisterView(APIView):
    """用户注册视图"""
    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="用户注册",
        description="使用用户名和密码进行注册",
        request=RegisterSerializer,
        responses={
            200: {
                "description": "注册成功",
                "content": {
                    "application/json": {
                        "example": {
                            "code": 200,
                            "message": "注册成功",
                            "data": {
                                "user": {
                                    "id": 1,
                                    "username": "test",
                                    "nickname": "测试用户",
                                    "avatar": "",
                                    "mobile": ""
                                },
                                "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
                            }
                        }
                    }
                }
            },
            400: {
                "description": "注册失败",
                "content": {
                    "application/json": {
                        "example": {
                            "code": 400,
                            "message": "用户名已存在"
                        }
                    }
                }
            }
        }
    )
    def post(self, request):
        """用户注册"""
        serializer = RegisterSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({"code": 400, "message": "参数错误", "errors": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

        username = serializer.validated_data['username']
        password = serializer.validated_data['password']

        if User.objects.filter(username=username).exists():
            return Response({"code": 400, "message": "用户名已存在"}, status=status.HTTP_400_BAD_REQUEST)

        user = User.objects.create_user(username=username, password=password)
        refresh = RefreshToken.for_user(user)
        return Response({
            "code": 200,
            "message": "注册成功",
            "data": {
                "user": MemberSerializer(user).data,
                "token": str(refresh.access_token)
            }
        })


class PhoneLoginView(APIView):
    """手机号登录视图"""
    permission_classes = []
    serializer_class = PhoneLoginSerializer

    @extend_schema(
        summary="手机号登录",
        description="使用手机号快捷登录",
        responses={200: OpenApiResponse(response=TokenResponse, description='登录成功')},
        tags=['用户']
    )
    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        code = serializer.validated_data['code']
        js_code = serializer.validated_data.get('js_code')

        try:
            # 1. 获取 openid
            if not js_code:
                return Response({
                    'code': 400,
                    'message': '登录凭证不能为空'
                }, status=400)

            session_info = code2session(js_code)
            if not session_info:
                return Response({
                    'code': 400,
                    'message': '获取用户信息失败'
                }, status=400)

            openid = session_info.get('openid')
            if not openid:
                return Response({
                    'code': 400,
                    'message': '获取用户信息失败'
                }, status=400)

            # 2. 获取手机号
            access_token = get_access_token()
            if not access_token:
                return Response({
                    'code': 400,
                    'message': '获取 access_token 失败'
                }, status=400)

            phone_info = get_phone_number(access_token, code)
            if not phone_info:
                return Response({
                    'code': 400,
                    'message': '获取手机号失败'
                }, status=400)

            phone_number = phone_info.get('phoneNumber')
            if not phone_number:
                return Response({
                    'code': 400,
                    'message': '获取手机号失败'
                }, status=400)

            # 3. 处理用户逻辑
            try:
                # 先尝试通过 openid 查找用户
                user = User.objects.get(openid=openid)
                # 更新手机号
                user.mobile = phone_number
                user.save()
            except User.DoesNotExist:
                # 如果通过 openid 找不到用户，则创建新用户
                username = f'user_{uuid.uuid4().hex[:8]}'
                password = uuid.uuid4().hex[:16]
                user = User.objects.create_user(
                    username=username,
                    password=password,
                    mobile=phone_number,
                    openid=openid
                )

            # 4. 生成 token
            refresh = RefreshToken.for_user(user)
            
            return Response({
                'code': 200,
                'message': '登录成功',
                'data': {
                    'user': {
                        'id': user.id,
                        'username': user.username,
                        'nickname': user.nickname,
                        'mobile': user.mobile,
                        'avatar': user.avatar.url if user.avatar else None
                    },
                    'token': str(refresh.access_token),
                    'refresh': str(refresh)
                }
            })

        except Exception as e:
            logger.error(f'手机号登录失败: {str(e)}')
            return Response({
                'code': 500,
                'message': f'登录失败: {str(e)}'
            }, status=500)


class UserInfoViewSet(viewsets.ModelViewSet):
    """用户信息视图集"""
    queryset = User.objects.all()
    serializer_class = MemberSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        return self.request.user

    @extend_schema(
        summary="获取用户信息",
        description="获取当前登录用户的详细信息",
        responses={
            200: MemberSerializer,
            401: {
                "description": "未登录",
                "content": {
                    "application/json": {
                        "example": {
                            "detail": "身份认证信息未提供。"
                        }
                    }
                }
            }
        }
    )
    def retrieve(self, request, *args, **kwargs):
        """获取用户信息"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response({
            "code": 200,
            "message": "success",
            "data": serializer.data
        })

    @extend_schema(
        summary="修改昵称",
        description="修改当前登录用户的昵称",
        request=UpdateNicknameSerializer,
        responses={
            200: {
                "description": "修改成功",
                "content": {
                    "application/json": {
                        "example": {
                            "code": 200,
                            "message": "修改成功",
                            "data": {
                                "nickname": "新昵称"
                            }
                        }
                    }
                }
            }
        }
    )
    @action(detail=False, methods=['put'])
    def update_nickname(self, request):
        """修改昵称"""
        serializer = UpdateNicknameSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({"code": 400, "message": "参数错误", "errors": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

        user = self.get_object()
        user.nickname = serializer.validated_data['nickname']
        user.save()

        return Response({
            "code": 200,
            "message": "修改成功",
            "data": {
                "nickname": user.nickname
            }
        })

    @extend_schema(
        summary="上传头像",
        description="上传并更新用户头像",
        request={
            'multipart/form-data': {
                'type': 'object',
                'properties': {
                    'avatar': {
                        'type': 'string',
                        'format': 'binary'
                    }
                }
            }
        },
        responses={
            200: {
                "description": "上传成功",
                "content": {
                    "application/json": {
                        "example": {
                            "code": 200,
                            "message": "上传成功",
                            "data": {
                                "avatar": "http://example.com/avatar.jpg"
                            }
                        }
                    }
                }
            }
        }
    )
    @action(detail=False, methods=['post'])
    def update_avatar(self, request):
        """上传头像"""
        if 'avatar' not in request.FILES:
            return Response({"code": 400, "message": "请选择要上传的图片"}, status=status.HTTP_400_BAD_REQUEST)

        user = self.get_object()
        user.avatar = request.FILES['avatar']
        user.save()

        return Response({
            "code": 200,
            "message": "success",
            "data": {
                "avatar": user.avatar.url if user.avatar else ""
            }
        })
