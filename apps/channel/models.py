import os
from datetime import datetime

from config.models import BaseModel
from django.contrib.auth import get_user_model
from django.db import models

User = get_user_model()


def generate_filename(instance, filename):
    now = datetime.now()
    timestamp = now.strftime("%Y%m%d%H%M%S")
    extension = filename.split(".")[-1]
    new_filename = f"{timestamp}.{extension}"
    return os.path.join("channel", new_filename)


# 贷款渠道模型
class Channel(BaseModel):
    # 渠道名称
    name = models.CharField(max_length=20, verbose_name="渠道名称")
    # 渠道图标 图片类型
    icon = models.ImageField(
        upload_to=generate_filename,
        default="channel/default.png",
        null=True,
        blank=True,
        verbose_name="渠道图标",
    )
    member = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="channels", verbose_name="会员"
    )
    # 状态,布尔类型
    status = models.BooleanField(default=True, verbose_name="状态")

    class Meta:
        verbose_name = "渠道"
        verbose_name_plural = verbose_name
        ordering = ["-create_time"]

    # 显示渠道名称和图标
    def __str__(self):
        return self.name
