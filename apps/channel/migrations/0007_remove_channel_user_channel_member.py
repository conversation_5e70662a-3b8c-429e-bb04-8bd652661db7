# Generated by Django 4.2.4 on 2023-08-23 08:45

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('channel', '0006_channel_user'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='channel',
            name='user',
        ),
        migrations.AddField(
            model_name='channel',
            name='member',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='channels', to=settings.AUTH_USER_MODEL, verbose_name='会员'),
            preserve_default=False,
        ),
    ]
