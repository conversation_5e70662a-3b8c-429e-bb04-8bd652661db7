# Generated by Django 4.2.4 on 2023-08-15 14:58

from django.db import migrations, models
import apps

class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Channel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=20, verbose_name='渠道名称')),
                ('icon', models.ImageField(upload_to=apps.channel.models.generate_filename, verbose_name='渠道图标')),
                ('status', models.BooleanField(default=True, verbose_name='状态')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '渠道',
                'verbose_name_plural': '渠道',
            },
        ),
    ]
