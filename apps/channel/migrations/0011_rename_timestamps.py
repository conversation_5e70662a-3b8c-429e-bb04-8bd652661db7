from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('channel', '0010_update_model'),
    ]

    operations = [
        migrations.RunSQL(
            sql="""
            SELECT COLUMN_NAME 
            INTO @old_name
            FROM information_schema.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = 'channel_channel'
                AND COLUMN_NAME IN ('updated_time', 'updated_at', 'update_time');

            SET @rename_sql = IF(@old_name IS NOT NULL AND @old_name != 'update_time',
                CONCAT('ALTER TABLE channel_channel RENAME COLUMN ', @old_name, ' TO update_time'),
                'SELECT 1');

            PREPARE stmt FROM @rename_sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            """
        ),
    ] 