from django.contrib import admin
from django.utils.safestring import mark_safe

from .models import Channel


@admin.register(Channel)
class ChannelAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'member', 'status', 'image_tag')  # 列表视图显示的字段
    list_display_links = ('id', 'name')  # 列表视图中可点击跳转的字段

    # 添加一个显示图标的方法
    def image_tag(self, obj):
        if obj.icon:
            return mark_safe(f'<a href="{obj.icon.url}" target="_blank"><img src="{obj.icon.url}" width="25px" /></a>')
        else:
            return "No Icon"

    image_tag.short_description = '图标'  # 设置字段在列表中的名称
