import os
import django

# 设置DJANGO_SETTINGS_MODULE环境变量为你的Django项目的配置模块
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")

# 初始化Django
django.setup()

from apps.channel.models import Channel


# 定义一个函数来批量添加数据
def create_channels():
    # 定义要添加的渠道数据列表
    channel_data = [
        {
            "name": "上海银行",
            "icon": "channel/上海银行.png",
            "status": True
        },
        {
            "name": "东营市商业银行",
            "icon": "channel/东营市商业银行.png",
            "status": True
        },
        {
            "name": "中信银行",
            "icon": "channel/中信银行.png",
            "status": True
        },
        {
            "name": "中原银行",
            "icon": "channel/中原银行.png",
            "status": True
        },
        {
            "name": "中国银行",
            "icon": "channel/中国银行.png",
            "status": True
        },
        {
            "name": "交通银行",
            "icon": "channel/交通银行.png",
            "status": True
        },
        {
            "name": "众邦银行",
            "icon": "channel/众邦银行.png",
            "status": True
        },
        {
            "name": "借呗",
            "icon": "channel/借呗.png",
            "status": True
        },
        {
            "name": "光大银行",
            "icon": "channel/光大银行.png",
            "status": True
        },
        {
            "name": "兴业银行",
            "icon": "channel/兴业银行.png",
            "status": True
        },
        {
            "name": "农业银行",
            "icon": "channel/农业银行.png",
            "status": True
        },
        {
            "name": "农村信用社",
            "icon": "channel/农村信用社.png",
            "status": True
        },
        {
            "name": "北京银行",
            "icon": "channel/北京银行.png",
            "status": True
        },
        {
            "name": "华夏银行",
            "icon": "channel/华夏银行.png",
            "status": True
        },
        {
            "name": "南京银行",
            "icon": "channel/南京银行.png",
            "status": True
        },
        {
            "name": "厦门国际银行",
            "icon": "channel/厦门国际银行.png",
            "status": True
        },
        {
            "name": "台州银行",
            "icon": "channel/台州银行.png",
            "status": True
        },
        {
            "name": "嘉兴银行",
            "icon": "channel/嘉兴银行.png",
            "status": True
        },
        {
            "name": "大连银行",
            "icon": "channel/大连银行.png",
            "status": True
        },
        {
            "name": "天津农商银行",
            "icon": "channel/天津农商银行.png",
            "status": True
        },
        {
            "name": "天津银行",
            "icon": "channel/天津银行.png",
            "status": True
        },
        {
            "name": "宁波银行",
            "icon": "channel/宁波银行.png",
            "status": True
        },
        {
            "name": "工商银行",
            "icon": "channel/工商银行.png",
            "status": True
        },
        {
            "name": "平安银行",
            "icon": "channel/平安银行.png",
            "status": True
        },
        {
            "name": "广发银行",
            "icon": "channel/广发银行.png",
            "status": True
        },
        {
            "name": "广州商业银行",
            "icon": "channel/广州商业银行.png",
            "status": True
        },
        {
            "name": "广州银行",
            "icon": "channel/广州银行.png",
            "status": True
        },
        {
            "name": "建设银行",
            "icon": "channel/建设银行.png",
            "status": True
        },
        {
            "name": "德州商业银行",
            "icon": "channel/德州商业银行.png",
            "status": True
        },
        {
            "name": "徽商银行",
            "icon": "channel/徽商银行.png",
            "status": True
        },
        {
            "name": "恒丰银行",
            "icon": "channel/恒丰银行.png",
            "status": True
        },
        {
            "name": "成都银行",
            "icon": "channel/成都银行.png",
            "status": True
        },
        {
            "name": "招商银行",
            "icon": "channel/招商银行.png",
            "status": True
        },
        {
            "name": "昆山农商行",
            "icon": "channel/昆山农商行.png",
            "status": True
        },
        {
            "name": "晋商银行",
            "icon": "channel/晋商银行.png",
            "status": True
        },
        {
            "name": "杭州联合银行",
            "icon": "channel/杭州联合银行.png",
            "status": True
        },
        {
            "name": "杭州银行",
            "icon": "channel/杭州银行.png",
            "status": True
        },
        {
            "name": "民生银行",
            "icon": "channel/民生银行.png",
            "status": True
        },
        {
            "name": "江苏银行",
            "icon": "channel/江苏银行.png",
            "status": True
        },
        {
            "name": "泰隆银行",
            "icon": "channel/泰隆银行.png",
            "status": True
        },
        {
            "name": "浙商银行",
            "icon": "channel/浙商银行.png",
            "status": True
        },
        {
            "name": "浙江民泰商业银行",
            "icon": "channel/浙江民泰商业银行.png",
            "status": True
        },
        {
            "name": "浦发银行",
            "icon": "channel/浦发银行.png",
            "status": True
        },
        {
            "name": "深圳发展银行",
            "icon": "channel/深圳发展银行.png",
            "status": True
        },
        {
            "name": "渤海银行",
            "icon": "channel/渤海银行.png",
            "status": True
        },
        {
            "name": "温州银行",
            "icon": "channel/温州银行.png",
            "status": True
        },
        {
            "name": "湖州市商业银行",
            "icon": "channel/湖州市商业银行.png",
            "status": True
        },
        {
            "name": "百度有钱花",
            "icon": "channel/百度有钱花.png",
            "status": True
        },
        {
            "name": "盛京银行",
            "icon": "channel/盛京银行.png",
            "status": True
        },
        {
            "name": "稠州商业银行",
            "icon": "channel/稠州商业银行.png",
            "status": True
        },
        {
            "name": "绍兴银行",
            "icon": "channel/绍兴银行.png",
            "status": True
        },
        {
            "name": "网商贷",
            "icon": "channel/网商贷.png",
            "status": True
        },
        {
            "name": "花呗",
            "icon": "channel/花呗.png",
            "status": True
        },
        {
            "name": "赣州银行",
            "icon": "channel/赣州银行.png",
            "status": True
        },
        {
            "name": "邮政储蓄银行",
            "icon": "channel/邮政储蓄银行.png",
            "status": True
        },
        {
            "name": "鄞州银行",
            "icon": "channel/鄞州银行.png",
            "status": True
        },
        {
            "name": "金华银行",
            "icon": "channel/金华银行.png",
            "status": True
        },
        {
            "name": "银行卡",
            "icon": "channel/银行卡.png",
            "status": True
        },
        {
            "name": "长沙银行",
            "icon": "channel/长沙银行.png",
            "status": True
        },
        {
            "name": "青岛银行",
            "icon": "channel/青岛银行.png",
            "status": True
        }
    ]

    # 遍历数据列表并创建渠道实例
    for data in channel_data:
        channel = Channel(**data)
        channel.save()


if __name__ == '__main__':
    # 调用创建函数来批量添加数据
    create_channels()
