from rest_framework import serializers

from .models import Channel


class ChannelInputSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=20, min_length=1, help_text="渠道名称")

    def create(self, validated_data):
        member = validated_data.pop('member')
        return Channel.objects.create(member=member, **validated_data)
    
    def update(self, instance, validated_data):
        instance.name = validated_data.get('name', instance.name)
        instance.save()
        return instance


class ChannelOutputSerializer(serializers.ModelSerializer):
    class Meta:
        model = Channel
        fields = ['id', 'name', 'icon']


class ChannelListOutputSerializer(serializers.Serializer):
    index = serializers.CharField(help_text="首字母")
    children = ChannelOutputSerializer(many=True, help_text="渠道列表")
