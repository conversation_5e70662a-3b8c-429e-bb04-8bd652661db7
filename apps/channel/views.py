from pypinyin import Style, pinyin
from rest_framework import permissions, status, views
from rest_framework.response import Response
from rest_framework_simplejwt.authentication import JWTAuthentication
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
from drf_spectacular.types import OpenApiTypes
from django.db.models import Q

from utils.permissions import IsOwner
from .models import Channel
from .serializers import ChannelInputSerializer, ChannelOutputSerializer, ChannelListOutputSerializer


class ChannelListCreateView(views.APIView):
    """
    渠道列表和创建接口
    
    提供渠道的列表查询和创建功能。
    - 列表查询：返回按拼音首字母分组的渠道列表
    - 创建渠道：创建新的贷款渠道
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="获取渠道列表",
        description="获取所有贷款渠道。管理员可以查看所有渠道，普通用户只能查看自己创建的渠道。",
        responses={
            200: OpenApiExample(
                'Success Response',
                value=[{
                    "index": "J",
                    "children": [{
                        "id": 1,
                        "name": "建设银行",
                        "icon": "channel/default.png"
                    }]
                }],
                response_only=True,
            ),
        },
        tags=['渠道管理']
    )
    def get(self, request):
        """获取渠道列表"""
        # 获取管理员创建的渠道和用户自己创建的渠道
        channels = Channel.objects.filter(
            Q(member=request.user) |  # 用户自己的渠道
            Q(member__is_staff=True) |  # 管理员创建的渠道
            Q(member__is_superuser=True)  # 超级管理员创建的渠道
        ).distinct()

        # 按拼音排序并分组
        sorted_channels = sorted(channels, key=lambda x: ''.join([i[0] for i in pinyin(x.name, style=Style.NORMAL)]))
        
        # 按首字母分组
        grouped_channels = {}
        for channel in sorted_channels:
            first_letter = pinyin(channel.name[0], style=Style.FIRST_LETTER)[0][0].upper()
            if first_letter not in grouped_channels:
                grouped_channels[first_letter] = []
            grouped_channels[first_letter].append(channel)

        # 格式化数据
        result = []
        for index in sorted(grouped_channels.keys()):
            result.append({
                'index': index,
                'children': grouped_channels[index]
            })
        
        serializer = ChannelListOutputSerializer(result, many=True)
        return Response(serializer.data)

    @extend_schema(
        summary="创建渠道",
        description="创建新的贷款渠道。渠道创建者会自动设置为当前登录用户。",
        request=ChannelInputSerializer,
        responses={
            201: OpenApiExample(
                'Success Response',
                value={
                    "id": 1,
                    "name": "建设银行",
                    "icon": "channel/default.png"
                },
                response_only=True,
            ),
            400: OpenApiExample(
                'Error Response',
                value={
                    "name": ["渠道名称不能为空"]
                },
                response_only=True,
            )
        },
        tags=['渠道管理']
    )
    def post(self, request):
        """创建渠道"""
        serializer = ChannelInputSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        # 添加当前用户
        channel = serializer.save(member=request.user)

        output_serializer = ChannelOutputSerializer(channel)
        return Response(output_serializer.data, status=status.HTTP_201_CREATED)


class ChannelDetailView(views.APIView):
    """
    渠道详情接口
    
    提供渠道的详情查询、更新和删除功能。
    只有渠道的创建者可以操作。
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [permissions.IsAuthenticated, IsOwner]

    def get_object(self, pk):
        """获取渠道对象"""
        try:
            obj = Channel.objects.get(id=pk)
            self.check_object_permissions(self.request, obj)
            return obj
        except Channel.DoesNotExist:
            return None

    @extend_schema(
        summary="获取渠道详情",
        description="获取指定渠道的详细信息。只有渠道创建者可以访问。",
        parameters=[
            OpenApiParameter(
                name="pk",
                type=OpenApiTypes.INT,
                location=OpenApiParameter.PATH,
                description="渠道ID"
            )
        ],
        responses={
            200: OpenApiExample(
                'Success Response',
                value={
                    "id": 1,
                    "name": "建设银行",
                    "icon": "channel/default.png"
                },
                response_only=True,
            ),
            404: OpenApiExample(
                'Error Response',
                value="渠道不存在",
                response_only=True,
            )
        },
        tags=['渠道管理']
    )
    def get(self, request, pk):
        """获取渠道详情"""
        channel = self.get_object(pk)
        if not channel:
            return Response("渠道不存在", status=status.HTTP_404_NOT_FOUND)

        serializer = ChannelOutputSerializer(channel)
        return Response(serializer.data)

    @extend_schema(
        summary="更新渠道",
        description="更新指定渠道的信息。只有渠道创建者可以更新。",
        parameters=[
            OpenApiParameter(
                name="pk",
                type=OpenApiTypes.INT,
                location=OpenApiParameter.PATH,
                description="渠道ID"
            )
        ],
        request=ChannelInputSerializer,
        responses={
            200: OpenApiExample(
                'Success Response',
                value={
                    "id": 1,
                    "name": "建设银行",
                    "icon": "channel/default.png"
                },
                response_only=True,
            ),
            400: OpenApiExample(
                'Error Response',
                value={
                    "name": ["渠道名称不能为空"]
                },
                response_only=True,
            ),
            404: OpenApiExample(
                'Error Response',
                value="渠道不存在",
                response_only=True,
            )
        },
        tags=['渠道管理']
    )
    def put(self, request, pk):
        """更新渠道"""
        channel = self.get_object(pk)
        if not channel:
            return Response("渠道不存在", status=status.HTTP_404_NOT_FOUND)

        serializer = ChannelInputSerializer(channel, data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        channel = serializer.save()
        output_serializer = ChannelOutputSerializer(channel)
        return Response(output_serializer.data)

    @extend_schema(
        summary="删除渠道",
        description="删除指定的渠道。只有渠道创建者可以删除。",
        parameters=[
            OpenApiParameter(
                name="pk",
                type=OpenApiTypes.INT,
                location=OpenApiParameter.PATH,
                description="渠道ID"
            )
        ],
        responses={
            204: None,
            404: OpenApiExample(
                'Error Response',
                value="渠道不存在",
                response_only=True,
            )
        },
        tags=['渠道管理']
    )
    def delete(self, request, pk):
        """删除渠道"""
        channel = self.get_object(pk)
        if not channel:
            return Response("渠道不存在", status=status.HTTP_404_NOT_FOUND)

        channel.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
