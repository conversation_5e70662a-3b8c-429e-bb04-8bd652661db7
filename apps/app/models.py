from django.contrib.auth import get_user_model
from django.db import models

from config.models import BaseModel

User = get_user_model()


class WorthInput(BaseModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="用户")
    purchase_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="购买价格")
    area = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="面积")
    purchase_agent_fee_rate = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="购房中介费率")
    purchase_tax_rate = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="购房税率")
    renovation_cost = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="装修费用")
    down_payment_rate = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="首付比例")
    loan_term_years = models.IntegerField(verbose_name="贷款年限")
    loan_interest_rate = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="贷款利率")
    repayment_method = models.CharField(max_length=20, verbose_name="还款方式")
    payments_made = models.IntegerField(verbose_name="已还款期数")
    price_change_rate = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="房价变动率")
    sale_agent_fee_rate = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="卖房中介费率")


    class Meta:
        verbose_name = "房价亏损计算器输入"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.user.username + "的房价亏损计算器输入"


class WorthOutput(BaseModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="用户")
    worth_input = models.ForeignKey(WorthInput, on_delete=models.CASCADE, verbose_name="房价亏损计算器输入")
    purchase_price_per_sqm = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="购买时每平方米的价格")
    purchase_agent_fee = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="购房中介费")
    purchase_tax = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="购房税")
    down_payment = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="首付款金额")
    loan_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="贷款金额")
    total_interest_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="总利息金额")
    interest_paid = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="已支付的利息")
    sale_agent_fee = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="销售中介费")
    current_market_value = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="当前市场价值")
    loss_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="亏损金额")


    class Meta:
        verbose_name = "房价亏损计算器输出"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.user.username + "的房价亏损计算器输出"
