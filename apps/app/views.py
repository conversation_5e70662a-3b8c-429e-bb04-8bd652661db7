from drf_spectacular.utils import OpenApiExample, extend_schema
from rest_framework import permissions, status, views
from rest_framework.response import Response

from apps.core.loan_calculator import calculate_loan
from utils.worth import calculate_property_loss

from .serializers import (
    WorthInputSerializer,
    LoanCalcOutputSerializer,
    WorthCalcOutputSerializer,
    LoanCalcInputSerializer,
)


class BaseCalculatorView(views.APIView):
    """计算器基类"""
    permission_classes = [permissions.AllowAny]
    authentication_classes = []


class LoanCalculatorView(BaseCalculatorView):
    """贷款计算器"""

    @extend_schema(
        summary="贷款计算器",
        description="计算贷款每期还款详情，包括本金、利息、月供总额等信息",
        request=LoanCalcInputSerializer,
        responses={200: LoanCalcOutputSerializer(many=True)},
        examples=[
            OpenApiExample(
                "请求示例",
                summary="计算贷款示例",
                description="计算50000元贷款，年利率24%，36期，等额本息方式的还款计划",
                value={
                    "principal": 50000,
                    "interest_rate": 24,
                    "months": 36,
                    "payment_type": "等额本息"
                },
                request_only=True
            ),
            OpenApiExample(
                "成功响应",
                summary="计算结果示例",
                description="返回每期还款详情列表",
                value=[
                    {
                        "period": 1,
                        "total_payment": 2046.86,
                        "principal": 1046.86,
                        "interest": 1000.00,
                        "remaining_principal": 48953.14
                    },
                    {
                        "period": 2,
                        "total_payment": 2046.86,
                        "principal": 1067.80,
                        "interest": 979.06,
                        "remaining_principal": 47885.34
                    }
                ],
                response_only=True
            )
        ]
    )
    def post(self, request):
        """计算贷款"""
        serializer = LoanCalcInputSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            result = calculate_loan(
                principal=serializer.validated_data['principal'],
                annual_rate=serializer.validated_data['interest_rate'],
                months=serializer.validated_data['months'],
                payment_type=serializer.validated_data['payment_type']
            )
            return Response(result, status=status.HTTP_200_OK)
        except ValueError as e:
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class WorthCalculatorView(BaseCalculatorView):
    """房价亏损计算器"""

    @extend_schema(
        summary="房价亏损计算器",
        description="计算房价亏损金额",
        request=WorthInputSerializer,
        responses={200: WorthCalcOutputSerializer},
        examples=[
            OpenApiExample(
                "请求示例",
                summary="计算房价亏损示例",
                description="计算房价亏损金额",
                value={
                    "purchase_price": 200,  # 单位:万元
                    "area": 100,
                    "purchase_agent_fee_rate": 2,
                    "purchase_tax_rate": 2,
                    "renovation_cost": 15,
                    "down_payment_rate": 30,
                    "loan_term_years": 30,
                    "loan_interest_rate": 4.25,
                    "repayment_method": "equal_installments",
                    "payments_made": 24,
                    "price_change_rate": -10,
                    "sale_agent_fee_rate": 2
                },
                request_only=True
            ),
            OpenApiExample(
                "成功响应",
                value={
                    "total_loss": 100000.00,
                    "monthly_payment": 2922.17,
                    "total_payment": 105197.92,
                    "loan_amount": 100000.00,
                    "down_payment": 300000.00,
                    "remaining_principal": 97482.00,
                    "current_value": 900000.00,
                },
            )
        ],
    )
    def post(self, request):
        """计算房价亏损"""
        serializer = WorthInputSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        # 获取验证后的数据
        data = serializer.validated_data.copy()
        
        # 转换金额单位（万元转换为元）
        data['purchase_price'] = data['purchase_price'] * 10000
        data['renovation_cost'] = data['renovation_cost'] * 10000

        result = calculate_property_loss(**data)

        output_serializer = WorthCalcOutputSerializer(data=result)
        output_serializer.is_valid(raise_exception=True)

        return Response(output_serializer.data)
