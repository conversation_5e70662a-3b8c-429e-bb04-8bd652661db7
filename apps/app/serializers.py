from rest_framework import serializers

from .models import WorthInput, WorthOutput

# 基础贷款输出序列化器
class BaseLoanOutputSerializer(serializers.Serializer):
    monthly_payment = serializers.FloatField(
        help_text="月供金额(单位:元) - 等额本息为固定值,等额本金为首期金额"
    )
    total_payment = serializers.FloatField(
        help_text="总还款金额(单位:元) - 包含本金和利息"
    )
    loan_amount = serializers.FloatField(
        help_text="贷款金额(单位:元)"
    )

# 基础房产序列化器
class BaseWorthSerializer(serializers.Serializer):
    purchase_price = serializers.FloatField(
        help_text="购买房屋的总价(单位:万元)"
    )
    area = serializers.FloatField(
        help_text="房屋的面积(单位:平方米)"
    )
    purchase_agent_fee_rate = serializers.FloatField(
        help_text="购房中介费率(%)",
        min_value=0,
        max_value=100
    )
    purchase_tax_rate = serializers.FloatField(
        help_text="购房税率(%)",
        min_value=0,
        max_value=100
    )
    renovation_cost = serializers.FloatField(
        help_text="装修费用(单位:万元)"
    )
    down_payment_rate = serializers.FloatField(
        help_text="首付款比率(%)",
        min_value=0,
        max_value=100
    )
    sale_agent_fee_rate = serializers.FloatField(
        help_text="卖房中介费率(%)",
        min_value=0,
        max_value=100
    )
    price_change_rate = serializers.FloatField(
        help_text="房价变动率(%)",
        default=0
    )

class WorthOutputSerializer(serializers.ModelSerializer):
    class Meta:
        model = WorthOutput
        fields = '__all__'

class WorthInputSerializer(BaseWorthSerializer, serializers.ModelSerializer):
    worth_output = WorthOutputSerializer(read_only=True)
    
    # 贷款相关字段
    loan_term_years = serializers.IntegerField(
        help_text="贷款年限 - 示例:30 - 取值范围:1-30",
        min_value=1,
        max_value=30
    )
    loan_interest_rate = serializers.FloatField(
        help_text="年利率(单位:%) - 示例:4.25 - 取值范围:0-100",
        min_value=0,
        max_value=100
    )
    repayment_method = serializers.CharField(
        help_text="还款方式 - 可选值:'equal_installments'(等额本息)或'equal_principal'(等额本金)"
    )
    payments_made = serializers.IntegerField(
        help_text="已经支付的月供数",
        default=0
    )

    class Meta:
        model = WorthInput
        fields = ('id', 'purchase_price', 'area', 'purchase_agent_fee_rate', 'purchase_tax_rate',
                  'renovation_cost', 'down_payment_rate', 'loan_term_years', 'loan_interest_rate', 
                  'repayment_method', 'payments_made', 'price_change_rate', 'sale_agent_fee_rate', 
                  'create_time', 'update_time', 'worth_output')

    def validate_repayment_method(self, value):
        if value not in ['equal_installments', 'equal_principal']:
            raise serializers.ValidationError("还款方式必须是'equal_installments'或'equal_principal'")
        return value

class LoanCalcInputSerializer(serializers.Serializer):
    principal = serializers.FloatField(
        help_text="贷款本金(单位:元)",
        min_value=0
    )
    interest_rate = serializers.FloatField(
        help_text="年利率(%) - 示例:24 表示24%",
        min_value=0,
        max_value=100
    )
    months = serializers.IntegerField(
        help_text="贷款期数(月) - 示例:36 表示36个月",
        min_value=1,
        max_value=360
    )
    payment_type = serializers.ChoiceField(
        help_text="还款方式",
        choices=["等额本息", "等额本金"]
    )

class LoanCalcOutputSerializer(serializers.Serializer):
    period = serializers.IntegerField(help_text="期数")
    total_payment = serializers.FloatField(help_text="月供总额(元)")
    principal = serializers.FloatField(help_text="本金(元)")
    interest = serializers.FloatField(help_text="利息(元)")
    remaining_principal = serializers.FloatField(help_text="剩余本金(元)")

class WorthCalcOutputSerializer(serializers.Serializer):
    purchase_price_per_sqm = serializers.FloatField(help_text="购买时每平方米的价格")
    purchase_agent_fee = serializers.FloatField(help_text="购房中介费")
    purchase_tax = serializers.FloatField(help_text="购房税")
    down_payment = serializers.FloatField(help_text="首付款金额")
    loan_amount = serializers.FloatField(help_text="贷款金额")
    total_interest_amount = serializers.FloatField(help_text="总利息金额")
    interest_paid = serializers.FloatField(help_text="已支付的利息")
    sale_agent_fee = serializers.FloatField(help_text="销售中介费")
    current_market_value = serializers.FloatField(help_text="当前市场价值")
    loss_amount = serializers.FloatField(help_text="亏损金额")
