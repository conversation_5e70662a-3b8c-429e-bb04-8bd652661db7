from django.contrib import admin

from .models import WorthInput, WorthOutput


@admin.register(WorthInput)
class WorthInputAdmin(admin.ModelAdmin):
    list_display = ['user', 'purchase_price', 'area', 'purchase_agent_fee_rate', 'purchase_tax_rate', 'renovation_cost',
                    'down_payment_rate', 'loan_term_years', 'loan_interest_rate', 'repayment_method', 'payments_made',
                    'price_change_rate', 'sale_agent_fee_rate', 'create_time', 'update_time']
    search_fields = ['user__username', 'user__email']
    date_hierarchy = 'create_time'
    ordering = ['-create_time']

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if request.user.is_superuser:
            return qs
        return qs.filter(user=request.user)


@admin.register(WorthOutput)
class WorthOutputAdmin(admin.ModelAdmin):
    list_display = ['user', 'worth_input', 'purchase_price_per_sqm', 'purchase_agent_fee', 'purchase_tax',
                    'down_payment',
                    'loan_amount', 'total_interest_amount', 'interest_paid', 'sale_agent_fee', 'current_market_value',
                    'loss_amount', 'create_time', 'update_time']
    search_fields = ['user__username', 'user__email']
    date_hierarchy = 'create_time'
    ordering = ['-create_time']

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if request.user.is_superuser:
            return qs
        return qs.filter(user=request.user)
