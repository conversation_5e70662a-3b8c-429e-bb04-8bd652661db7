# Generated by Django 4.2.4 on 2024-05-21 10:58

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='WorthInput',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('purchase_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='购买价格')),
                ('area', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='面积')),
                ('purchase_agent_fee_rate', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='购房中介费率')),
                ('purchase_tax_rate', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='购房税率')),
                ('renovation_cost', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='装修费用')),
                ('down_payment_rate', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='首付比例')),
                ('loan_term_years', models.IntegerField(verbose_name='贷款年限')),
                ('loan_interest_rate', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='贷款利率')),
                ('repayment_method', models.CharField(max_length=10, verbose_name='还款方式')),
                ('payments_made', models.IntegerField(verbose_name='已还款期数')),
                ('price_change_rate', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='房价变动率')),
                ('sale_agent_fee_rate', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='卖房中介费率')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '房价亏损计算器输入',
                'verbose_name_plural': '房价亏损计算器输入',
            },
        ),
        migrations.CreateModel(
            name='WorthOutput',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('purchase_price_per_sqm', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='购买时每平方米的价格')),
                ('purchase_agent_fee', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='购房中介费')),
                ('purchase_tax', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='购房税')),
                ('down_payment', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='首付款金额')),
                ('loan_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='贷款金额')),
                ('total_interest_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='总利息金额')),
                ('interest_paid', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='已支付的利息')),
                ('sale_agent_fee', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='销售中介费')),
                ('current_market_value', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='当前市场价值')),
                ('loss_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='亏损金额')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
                ('worth_input', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.worthinput', verbose_name='房价亏损计算器输入')),
            ],
            options={
                'verbose_name': '房价亏损计算器输出',
                'verbose_name_plural': '房价亏损计算器输出',
            },
        ),
    ]
