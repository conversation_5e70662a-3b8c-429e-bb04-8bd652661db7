# 配置数据库
DATABASE_NAME=repay_django
DATABASE_USER=repay_django
DATABASE_PASSWORD=zNTeCafJQ5H4QAfA
DATABASE_HOST=*************
DATABASE_PORT=33060

# 是否开启调试模式
DEBUG=True

# 又拍云短信配置
UPYUN_SMS_TOKEN=Xezs26iLkoHXCq5FYca0IIFHKwnDMP
UPYUN_SMS_TEMPLATE_ID=your_template_id

# redis配置
CELERY_BROKER_URL=redis://default:<EMAIL>:46386
CELERY_RESULT_BACKEND=redis://default:<EMAIL>:46386

# Spug短信配置
SPUG_USER_ID=1fe193449d3e4a138cdc4270b605df80  # 替换为实际的用户ID
SPUG_REPAYMENT_TEMPLATE_ID=GWorxoDljOAQ  # 替换为实际的还款提醒模板ID
SPUG_OVERDUE_TEMPLATE_ID=GWorxoDljOAQ  # 替换为实际的逾期提醒模板ID

