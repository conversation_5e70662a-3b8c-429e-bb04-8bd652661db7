from rest_framework import permissions


class IsAdminUserOrReadOnly(permissions.BasePermission):
    """
    自定义权限，只有管理员用户才有权限进行修改操作
    """

    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:  # 如果请求方法是安全方法，如GET、HEAD、OPTIONS
            return True
        return request.user and request.user.is_staff


class IsOwner(permissions.BasePermission):
    """
    自定义权限，只允许对象的创建者访问和操作
    """

    def has_object_permission(self, request, view, obj):
        # 仅允许对象的创建者访问和操作
        return obj.member == request.user
