import math
from datetime import datetime, timedelta

from dateutil.relativedelta import relativedelta


def round_decimal(num):
    """
    将小数保留两位小数。

    参数:
    - num: 需要保留两位小数的数值

    返回:
    - 保留两位小数后的结果
    """
    return round(num * 100) / 100


class LoanCalculator:
    def __init__(self, amount, installments_number, interest_rate, start_date, diminishing=False):
        """
        LoanCalculator类的构造函数。

        参数:
        - amount: 贷款金额
        - installments_number: 分期数
        - interest_rate: 利率
        - start_date: 贷款开始日期
        - diminishing: 是否使用等额本金贷款，默认为False
        """
        self.amount = amount  # 贷款金额
        self.installments_number = installments_number  # 分期数
        self.interest_rate = interest_rate  # 利率
        self.start_date = start_date  # 贷款开始日期
        self.diminishing = diminishing  # 是否使用等额本金贷款

    def get_next_installment(self, current_installment, capital_sum, interest_sum, installment_date):
        """
        计算下一期的本金、利息和总额，并设置还款日期。

        参数:
        - current_installment: 当前期数
        - capital_sum: 已还本金总额
        - interest_sum: 已还利息总额
        - installment_date: 当前还款日期

        返回:
        - 包含本金、利息、总额、期数和还款日期等信息的字典
        """
        capital = None  # 本金
        interest = None  # 利息
        installment = None  # 总额
        irm_pow = None  # 利息和本金的比值
        interest_rate_month = self.interest_rate / 1200  # 月利率

        if self.diminishing:
            # 对于等额本息贷款，计算本金、利息和总额
            capital = round_decimal(self.amount / self.installments_number)
            interest = round_decimal((self.amount - capital_sum) * interest_rate_month)
            installment = capital + interest
        else:
            # 对于等额本金贷款，计算本金、利息和总额
            irm_pow = math.pow(1 + interest_rate_month, self.installments_number)
            installment = round_decimal(self.amount * (interest_rate_month * irm_pow / (irm_pow - 1)))
            interest = round_decimal((self.amount - capital_sum) * interest_rate_month)
            capital = installment - interest

        # 设置还款日期为下一个还款周期的日期
        next_installment_date = installment_date + relativedelta(months=1)

        # 剩余未还本金
        remain_capital = round_decimal(self.amount - capital_sum)
        # 计算剩余未还本金+利息
        remain_capital_interest = round_decimal(remain_capital + interest)

        return {
            'installment_number': current_installment,  # 期数
            'capital': round_decimal(capital),  # 本金
            'interest': interest,  # 利息
            'installment': installment,  # 总额
            'remain_capital': remain_capital,  # 剩余未还本金
            'interest_sum': round_decimal(interest_sum + interest),  # 已还利息总额
            'installment_date': next_installment_date  # 还款日期
        }

    def calculate_loan(self):
        """
        计算贷款的还款计划和统计信息。

        返回:
        - 包含还款计划、贷款金额、总利息、已还本金和总额等信息的字典
        """
        if not self.amount or self.amount <= 0 or not self.installments_number or self.installments_number <= 0 or not self.interest_rate or self.interest_rate <= 0:
            raise ValueError(
                "Invalid parameters: {} {} {}".format(self.amount, self.installments_number, self.interest_rate))

        installments = []  # 还款计划
        interest_sum = 0  # 已还利息总额
        capital_sum = 0  # 已还本金总额
        total_sum = 0  # 总额
        installment_date = self.start_date  # 还款日期

        for i in range(1, self.installments_number + 1):
            # 计算每一期的还款信息
            installment = self.get_next_installment(i, capital_sum, interest_sum, installment_date)  # 获取下一期的还款信息
            total_sum += installment['installment']  # 总额
            capital_sum += installment['capital']  # 已还本金总额
            interest_sum += installment['interest']  # 已还利息总额
            installment_date = installment['installment_date']  # 还款日期

            if i == self.installments_number:
                # 最后一期修正
                capital_sum += installment['remain_capital']  # 已还本金总额
                total_sum += installment['remain_capital']  # 总额

            installments.append(installment)  # 添加还款信息到还款计划

        return {
            'installments': installments,  # 还款计划
            'amount': round_decimal(self.amount),  # 贷款金额
            'interest_sum': round_decimal(interest_sum),  # 总利息
            'capital_sum': round_decimal(capital_sum),  # 已还本金
            'total_sum': round_decimal(total_sum)  # 总额
        }
