import httpx
from typing import Dict, Optional
from django.conf import settings
from django.core.cache import cache
from apps.member.models import SMSRecord


class SMSService:
    """Spug短信服务工具类"""

    def __init__(self):
        """初始化短信服务"""
        self.base_url = 'https://push.spug.cc'
        self.timeout = 10

    def _validate_phone(self, phone: str) -> Optional[str]:
        """验证手机号格式

        Args:
            phone: 手机号码

        Returns:
            错误信息,如果验证通过返回None
        """
        if not phone:
            return "手机号码不能为空"

        if not isinstance(phone, str) or not phone.isdigit() or len(phone) != 11:
            return "手机号码格式不正确"

        return None

    def _check_send_frequency(self, phone: str, interval: int = 60) -> Optional[str]:
        """检查发送频率限制

        Args:
            phone: 手机号码
            interval: 限制间隔(秒)

        Returns:
            错误信息,如果可以发送返回None
        """
        cache_key = f'sms_limit_{phone}'
        if cache.get(cache_key):
            return f'发送过于频繁,请{interval}秒后再试'

        # 设置发送限制
        cache.set(cache_key, True, interval)
        return None

    def _save_sms_record(self, member, phone: str, content: str, status: bool,
                        error_message: Optional[str] = None,
                        repayment_id: Optional[int] = None,
                        sms_type: str = 'repayment') -> None:
        """保存短信发送记录"""
        # 如果是测试发送(member为None),则不保存记录
        if member is None:
            return

        SMSRecord.objects.create(
            member=member,
            phone=phone,
            content=content,
            status=status,
            error_message=error_message,
            repayment_id=repayment_id,
            sms_type=sms_type
        )

    def _send_sms_template(self, member, phone: str, template_id: str, code: str,
                         record_content: str, repayment_id: Optional[int] = None,
                         sms_type: str = 'repayment') -> Optional[str]:
        """使用模板发送短信的通用方法

        Args:
            member: 用户对象
            phone: 手机号码
            template_id: 短信模板ID
            code: 验证码或其他参数
            record_content: 用于记录的短信内容
            repayment_id: 还款计划ID
            sms_type: 短信类型

        Returns:
            发送失败返回错误信息,成功返回None
        """
        # 验证手机号
        error_msg = self._validate_phone(phone)
        if error_msg:
            if member is not None:
                self._save_sms_record(member, phone or '', record_content, False, error_msg, repayment_id, sms_type)
            return error_msg

        # 检查发送频率
        error_msg = self._check_send_frequency(phone)
        if error_msg:
            if member is not None:
                self._save_sms_record(member, phone, record_content, False, error_msg, repayment_id, sms_type)
            return error_msg

        try:
            # 构建请求URL - 使用模板ID方式发送短信
            url = f"{self.base_url}/send/{template_id}"

            # 构建请求参数
            params = {
                'code': code,
                'targets': phone
            }

            # 发送GET请求
            with httpx.Client(timeout=self.timeout) as client:
                response = client.get(url, params=params)
                try:
                    result = response.json()
                except ValueError:
                    # 响应不是有效的JSON格式
                    result = {}

            # 处理响应
            if response.status_code == 200:
                # 如果返回JSON格式且包含code字段，检查code值
                if isinstance(result, dict) and 'code' in result:
                    # 成功情况：code为0或200
                    if result.get('code') == 0 or result.get('code') == 200:
                        if member is not None:
                            self._save_sms_record(member, phone, record_content, True, None, repayment_id, sms_type)
                        return None
                    else:
                        error_msg = f'发送失败: {result.get("message", "未知错误")}'
                        if member is not None:
                            self._save_sms_record(member, phone, record_content, False, error_msg, repayment_id, sms_type)
                        return error_msg
                else:
                    # 如果没有code字段但状态码是200，假设成功
                    if member is not None:
                        self._save_sms_record(member, phone, record_content, True, None, repayment_id, sms_type)
                    return None
            else:
                error_msg = f'发送失败: HTTP状态码 {response.status_code}'
                if member is not None:
                    self._save_sms_record(member, phone, record_content, False, error_msg, repayment_id, sms_type)
                return error_msg

        except httpx.RequestError as e:
            error_msg = f"网络请求异常: {str(e)}"
            if member is not None:
                self._save_sms_record(member, phone, record_content, False, error_msg, repayment_id, sms_type)
            return error_msg

        except Exception as e:
            error_msg = f'发送异常: {str(e)}'
            if member is not None:
                self._save_sms_record(member, phone, record_content, False, error_msg, repayment_id, sms_type)
            return error_msg

    def send_repayment_reminder(self, member, phone: str, params: Dict, repayment_id: Optional[int] = None) -> Optional[str]:
        """发送还款提醒短信

        Args:
            member: 用户对象
            phone: 手机号码
            params: 短信参数,必须包含name、date、amount字段
            repayment_id: 还款计划ID

        Returns:
            发送失败返回错误信息,成功返回None
        """
        # 获取模板ID
        template_id = settings.SPUG_REPAYMENT_TEMPLATE_ID

        # 构建短信内容 (用于记录，实际发送内容由模板决定)
        message = f"您的借款项目\"{params.get('name')}\"将于{params.get('date')}到期，应还金额{params.get('amount')}元，请及时还款。可在微信小程序打开随钱记变更还款状态。"
        record_content = f'【随钱记】{message}'

        # 构建code参数 (根据实际模板需要的参数调整)
        # 模板内容是"${key1}，请关注！"
        code = f"{params.get('name')}"

        # 调用模板发送方法
        return self._send_sms_template(member, phone, template_id, code, record_content, repayment_id, 'repayment')

    def send_overdue_reminder(self, member, phone: str, params: Dict, repayment_id: Optional[int] = None) -> Optional[str]:
        """发送逾期提醒短信

        Args:
            member: 用户对象
            phone: 手机号码
            params: 短信参数,必须包含name、amount字段
            repayment_id: 还款计划ID

        Returns:
            发送失败返回错误信息,成功返回None
        """
        # 获取模板ID
        template_id = settings.SPUG_OVERDUE_TEMPLATE_ID

        # 构建短信内容 (用于记录，实际发送内容由模板决定)
        message = f"您的借款项目\"{params.get('name')}\"已逾期，应还金额{params.get('amount')}元，请尽快还款，避免影响您的信用记录。"
        record_content = f'【随钱记】{message}'

        # 构建code参数 (根据实际模板需要的参数调整)
        # 模板内容是"${key1}，请关注！"
        code = f"{params.get('name')}"

        # 调用模板发送方法
        return self._send_sms_template(member, phone, template_id, code, record_content, repayment_id, 'overdue')


# 创建全局实例
sms_service = SMSService()