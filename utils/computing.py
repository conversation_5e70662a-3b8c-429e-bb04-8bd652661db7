"""
贷款计算器
"""


def calculate_loan(principal, interest_rate, months, payment_type):
    """
    计算贷款还款计划和总还款金额。

    :param principal: 贷款本金
    :param interest_rate: 年利率（百分比）
    :param months: 贷款期限（月份）
    :param payment_type: 还款方式（'等额本息' 或 '等额本金'）
    :return: 还款计划和总还款金额
    """
    # 将年利率转换为月利率
    monthly_interest_rate = interest_rate / 12 / 100

    if payment_type == '等额本息':
        # 计算等额本息方式的月还款额
        monthly_payment = principal * (monthly_interest_rate * (1 + monthly_interest_rate) ** months) / (
                (1 + monthly_interest_rate) ** months - 1)
        total_payment = monthly_payment * months

        # 生成还款计划
        payment_schedule = []
        remaining_balance = principal

        for month in range(1, months + 1):
            interest_payment = remaining_balance * monthly_interest_rate
            principal_payment = monthly_payment - interest_payment
            remaining_balance -= principal_payment
            payment_schedule.append({
                "期数": month,
                "本金还款": round(principal_payment, 2),  # 四舍五入取两位小数
                "利息还款": round(interest_payment, 2),
                "剩余本金": round(remaining_balance, 2),
                "月供": round(monthly_payment, 2)
            })

    elif payment_type == '等额本金':
        # 计算等额本金方式的月还款额
        monthly_principal_payment = principal / months
        total_payment = 0

        # 生成还款计划
        payment_schedule = []
        remaining_balance = principal

        for month in range(1, months + 1):
            interest_payment = remaining_balance * monthly_interest_rate
            total_payment += monthly_principal_payment + interest_payment
            remaining_balance -= monthly_principal_payment
            payment_schedule.append({
                "期数": month,
                "本金还款": round(monthly_principal_payment, 2),  # 四舍五入取两位小数
                "利息还款": round(interest_payment, 2),
                "剩余本金": round(remaining_balance, 2),
                "月供": round(monthly_principal_payment + interest_payment, 2)
            })

    else:
        return {"error": "不支持的还款方式"}

    return {
        # 每月平均还款
        "月还款额":  round(total_payment / months, 2),  # 四舍五入取两位小数
        "总还款金额": round(total_payment, 2),  # 四舍五入取两位小数
        "还款计划": payment_schedule,
        "累计利息": round(total_payment - principal, 2)  # 四舍五入取两位小数
    }
