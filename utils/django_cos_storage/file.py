from io import BytesIO
from shutil import copyfileobj
from tempfile import SpooledTemporaryFile

from django.core.files.base import File


class TencentCOSFile(File):
    """
    一个封装了腾讯云对象存储服务（COS）文件的类，继承自django的File类。

    参数:
    - name: 文件在COS中的名称。
    - storage: 用于访问COS存储桶的存储后端实例。
    """

    def __init__(self, name, storage):
        self.name = name  # 文件名
        self._storage = storage  # 存储后端
        self._file = None  # 文件的本地副本

    @property
    def file(self):
        """
        获取文件的属性。如果文件还未被下载，则从COS下载到一个临时文件中。

        返回:
        - 文件的BytesIO对象或SpooledTemporaryFile对象。
        """
        if self._file is None:
            self._file = SpooledTemporaryFile()  # 创建一个临时文件用于存储文件内容
            response = self._storage.client.get_object(
                Bucket=self._storage.bucket,
                Key=self.name,
            )  # 从COS获取文件
            raw_stream = response["Body"].get_raw_stream()
            with BytesIO(raw_stream.data) as file_content:  # 将获取到的文件内容包装成BytesIO对象
                copyfileobj(file_content, self._file)  # 将文件内容复制到临时文件
            self._file.seek(0)  # 重置文件指针到文件开头
        return self._file

    @file.setter
    def file(self, value):
        """
        设置文件属性。

        参数:
        - value: 要设置的文件对象。
        """
        self._file = value
