# -*- coding: utf-8 -*-
# 作者：mircool
# 创建时间：2024年05月19日
# 用途：房价亏损计算器

def calculate_property_loss(
        purchase_price,  # 购买房屋的总价
        area,  # 房屋的面积
        purchase_agent_fee_rate,  # 购房中介费率
        purchase_tax_rate,  # 购房税率
        renovation_cost,  # 装修费用
        down_payment_rate,  # 首付款比率
        loan_term_years,  # 贷款年限
        loan_interest_rate,  # 贷款利率
        repayment_method,  # 还款方式（'equal_installments'等额本息或'equal_principal'等额本金）
        payments_made,  # 已经支付的月供数
        price_change_rate,  # 房价变动率（正数表示增长，负数表示降低）
        sale_agent_fee_rate  # 销售中介费率
):
    """
    计算房产亏损的金额。

    参数:
    - purchase_price: 购买房屋的总价。
    - area: 房屋的面积。
    - purchase_agent_fee_rate: 购房中介费率。
    - purchase_tax_rate: 购房税率。
    - renovation_cost: 装修费用。
    - down_payment_rate: 首付款比率。
    - loan_term_years: 贷款年限。
    - loan_interest_rate: 贷款利率。
    - repayment_method: 还款方式（'equal_installments'等额本息或'equal_principal'等额本金）。
    - payments_made: 已经支付的月供数。
    - price_change_rate: 房价变动率（正数表示增长，负数表示降低）。
    - sale_agent_fee_rate: 销售中介费率。

    返回值:
    - 字典，包含以下键值对：
      - 'purchase_price_per_sqm': 购买时每平方米的价格。
      - 'purchase_agent_fee': 购房中介费。
      - 'purchase_tax': 购房税。
      - 'down_payment': 首付款金额。
      - 'loan_amount': 贷款金额。
      - 'total_interest_amount': 总利息金额。
      - 'interest_paid': 已支付的利息。
      - 'sale_agent_fee': 销售中介费。
      - 'current_market_value': 当前市场价值。
      - 'loss_amount': 亏损金额。
    """

    # 计算购房每平方米的价格
    purchase_price_per_sqm = purchase_price / area

    # 计算购房中介费
    purchase_agent_fee = purchase_price * (purchase_agent_fee_rate / 100)

    # 计算购房税
    purchase_tax = purchase_price * (purchase_tax_rate / 100)

    # 计算首付款金额
    down_payment = purchase_price * (down_payment_rate / 100)

    # 计算贷款金额
    loan_amount = purchase_price - down_payment

    # 计算月利率
    monthly_interest_rate = loan_interest_rate / 12 / 100

    # 计算总支付次数（月）
    total_payments = loan_term_years * 12

    # 初始化已支付利息和剩余贷款金额
    interest_paid = 0
    remaining_loan = loan_amount
    monthly_payment = 0

    # 根据还款方式计算月供和已还利息
    if repayment_method == 'equal_installments':
        monthly_payment = loan_amount * monthly_interest_rate * pow(1 + monthly_interest_rate, total_payments) / (
                pow(1 + monthly_interest_rate, total_payments) - 1)
        for i in range(payments_made):
            monthly_interest = remaining_loan * monthly_interest_rate
            remaining_loan -= (monthly_payment - monthly_interest)
            interest_paid += monthly_interest
    elif repayment_method == 'equal_principal':
        monthly_principal_payment = loan_amount / total_payments
        for i in range(payments_made):
            monthly_interest = remaining_loan * monthly_interest_rate
            remaining_loan -= monthly_principal_payment
            interest_paid += monthly_interest

    # 计算总利息
    total_interest = interest_paid
    remaining_loan = loan_amount - interest_paid
    for i in range(total_payments - payments_made):
        monthly_interest = remaining_loan * monthly_interest_rate
        remaining_loan -= (
                monthly_payment - monthly_interest) if repayment_method == 'equal_installments' else monthly_principal_payment
        total_interest += monthly_interest

    total_interest_amount = total_interest

    # 计算当前市场价值
    current_market_value = purchase_price * (1 + price_change_rate / 100)

    # 计算销售中介费
    sale_agent_fee = current_market_value * (sale_agent_fee_rate / 100)

    # 计算亏损金额
    total_cost = purchase_price + purchase_agent_fee + purchase_tax + renovation_cost + interest_paid + sale_agent_fee
    loss_amount = current_market_value - total_cost

    # 返回计算结果
    return {
        'purchase_price_per_sqm': purchase_price_per_sqm,  # 购买时每平方米的价格
        'purchase_agent_fee': purchase_agent_fee,  # 购房中介费
        'purchase_tax': purchase_tax,  # 购房税
        'down_payment': down_payment,  # 首付款金额
        'loan_amount': loan_amount,  # 贷款金额
        'total_interest_amount': total_interest_amount,  # 总利息金额
        'interest_paid': interest_paid,  # 已支付的利息
        'sale_agent_fee': sale_agent_fee,  # 销售中介费
        'current_market_value': current_market_value,  # 当前市场价值
        'loss_amount': loss_amount  # 亏损金额
    }

# 示例调用
# result = calculate_property_loss(
#     purchase_price=200,
#     area=100,
#     purchase_agent_fee_rate=0.02,
#     purchase_tax_rate=0.02,
#     renovation_cost=15,
#     down_payment_rate=0.3,
#     loan_term_years=30,
#     loan_interest_rate=4.25,
#     repayment_method='equal_installments',
#     payments_made=1,
#     price_change_rate=-10,
#     sale_agent_fee_rate=0.02
# )
#
# for k, v in result.items():
#     print(f'{k}: {v}')
#
