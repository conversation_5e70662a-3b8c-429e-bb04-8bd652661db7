import unittest

from worth import 房价亏损计算器


class TestHousePriceCalculator(unittest.TestCase):
    def test房价亏算计算器(self):
        # 输入参数
        购入总价 = 1000000
        房屋面积 = 100
        购入中介费率 = 0.01
        购入契费率 = 0.02
        装修费 = 50000
        首付费率 = 0.3
        贷款年限 = 20
        贷款利率 = 0.04
        还款方式 = '等额本息'
        已还期数 = 12
        涨跌幅度 = 0.05
        卖出中介费率 = 0.01

        # 调用函数
        结果 = 房价亏损计算器(购入总价, 房屋面积, 购入中介费率, 购入契费率, 装修费, 首付费率, 贷款年限, 贷款利率,
                              还款方式,
                              已还期数, 涨跌幅度, 卖出中介费率)

        # 验证结果中的某些预期值
        self.assertAlmostEqual(结果['购入单价'], 10000)   # 预期购入单价为 10000
        self.assertAlmostEqual(结果['购入中介费'], 10000)
        self.assertAlmostEqual(结果['购入契费'], 20000)
        self.assertAlmostEqual(结果['首付金额'], 300000)
        self.assertAlmostEqual(结果['贷款额'], 700000)
        # ... 对其他返回项进行断言检验


if __name__ == '__main__':
    unittest.main()
