from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client
from django.conf import settings

# 从腾讯云获取的配置信息
secret_id = 'AKIDoq2ItYyvU9tm4CANawFnDRlhmJO3gmoS'
secret_key = '9Z6raWn9HETY1fzEpYBkFSF45l7DyGcE'
region = 'ap-chengdu'
token = None  # 使用临时密钥需要传入 Token，默认为空，可不填

config = CosConfig(Region=region, SecretId=secret_id, SecretKey=secret_key, Token=token)
client = CosS3Client(config)


def upload_file(local_file_path, cos_path, bucket='repay-django-1251001265'):
    """
    上传文件到腾讯云COS
    :param local_file_path:  本地文件的路径
    :param cos_path: 上传到腾讯云COS的路径
    :param bucket: Bucket由BucketName-APPID组成
    :return:
    """
    response = client.upload_file(
        Bucket=bucket,  # Bucket由BucketName-APPID组成
        LocalFilePath=local_file_path,  # 本地文件的路径
        Key=cos_path,  # 上传到腾讯云COS的路径
    )
    return response
