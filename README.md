# 随钱记 - 贷款管理系统

## 短信提醒功能

系统集成了Spug短信服务,可以在用户还款当天自动发送提醒短信。

### 1. 依赖安装

项目使用了以下主要依赖:
- httpx>=0.28.1: 用于调用短信API
- celery==5.4.0: 用于执行定时任务
- redis==5.2.0: 作为Celery的消息代理和缓存服务

安装依赖:
```bash
pip install -r requirements.txt
```

### 2. Redis安装配置

Redis用于消息代理和短信发送频率限制:
1. 已安装Redis服务器
2. Redis服务正在运行
3. 可以通过配置的地址和端口访问

### 3. Spug短信配置

1. 注册Spug账号并开通短信服务
2. 在Spug个人中心页面获取用户ID
3. 创建短信模板并获取模板ID
4. 在`.env`文件中配置:
   ```bash
   # Spug短信配置
   SPUG_USER_ID=your_user_id  # 替换为实际的用户ID
   SPUG_REPAYMENT_TEMPLATE_ID=repayment_template  # 替换为实际的还款提醒模板ID
   SPUG_OVERDUE_TEMPLATE_ID=overdue_template  # 替换为实际的逾期提醒模板ID
   ```
5. 在Spug个人中心页面右侧「通道设置」中配置短信通道

4. 短信内容说明:

#### 还款提醒短信
短信内容示例:
```
【随钱记】尊敬的用户，您的借款项目"测试项目"将于2024-06-01到期，应还金额1000元，请及时还款。可在微信小程序打开随钱记变更还款状态。
```

代码中传递的参数格式:
```python
# 使用模板ID方式发送短信
template_id = settings.SPUG_REPAYMENT_TEMPLATE_ID
code = f"{name},{date},{amount}"  # 根据模板中的参数格式调整
params = {
    'code': code,
    'targets': phone
}

# 发送GET请求
response = httpx.get(f"https://push.spug.cc/send/{template_id}", params=params)
```

#### 逾期提醒短信
短信内容示例:
```
【随钱记】尊敬的用户，您的借款项目"测试项目"已逾期，应还金额1000元，请尽快还款，避免影响您的信用记录。
```

代码中传递的参数格式:
```python
# 使用模板ID方式发送短信
template_id = settings.SPUG_OVERDUE_TEMPLATE_ID
code = f"{name},{amount}"  # 根据模板中的参数格式调整
params = {
    'code': code,
    'targets': phone
}

# 发送GET请求
response = httpx.get(f"https://push.spug.cc/send/{template_id}", params=params)
```

### 4. 功能说明

1. 自动提醒:
   - 系统每天早上9点自动检查当天需要还款的记录
   - 对未还款且用户有手机号的记录发送提醒短信
   - 每个还款计划每天只会发送一次提醒

2. 发送频率限制:
   - 同一手机号60秒内只能发送一次短信
   - 可通过修改代码自定义限制时间
   - 使用Redis缓存实现限制功能

3. 余额查询:
   - Spug平台不提供API余额查询功能
   - 请登录Spug平台查看短信余额
   - SMSService.get_balance()方法将返回提示信息

4. 短信记录:
   - 系统会记录所有短信发送历史
   - 包含发送状态、错误信息等
   - 可在管理后台查看发送记录

5. 支持的短信类型:
   - 还款提醒(repayment)
   - 逾期提醒(overdue)
   - 其他通知(other)

### 5. 开发说明

1. 短信发送服务:
   - 实现在`utils/sms.py`
   - 使用`SMSService`类处理短信发送
   - 支持错误处理和频率限制

2. 定时任务:
   - 实现在`apps/repay/tasks.py`
   - 使用`@shared_task`装饰器定义任务
   - 可在`config/celery.py`中配置执行时间

3. 数据模型:
   - 短信记录模型在`apps/member/models.py`
   - 使用`SMSRecord`存储发送历史
   - 支持按用户、类型查询记录

### 6. 注意事项

1. 安全性:
   - 请妥善保管Spug平台的访问凭证
   - 建议使用环境变量或配置文件存储敏感信息
   - 定期检查短信发送记录,监控异常情况

2. 性能优化:
   - 使用数据库索引优化查询性能
   - 短信发送使用异步任务处理
   - 定期清理历史记录

3. 故障处理:
   - 检查Redis服务是否正常运行
   - 确认Spug用户ID配置是否正确
   - 查看Celery日志排查问题
   - 登录Spug平台检查短信余额是否充足
   - 确认Spug平台服务是否正常

### 7. 联系支持

如有问题请联系技术支持。

---

## 🌟 随钱记 - 您的智能贷款管理助手

### 📱 便捷管理，智能提醒

随钱记是一款专业的贷款管理工具，为您提供全方位的贷款管理解决方案。无论是个人贷款还是企业融资，随钱记都能帮您轻松管理，不错过每一个还款日期。

### 🚀 核心特色

#### 1. 智能提醒系统
- ⏰ 自动还款提醒，告别逾期烦恼
- 📊 多渠道提醒，短信+小程序双重保障
- 🎯 精准推送，确保信息及时送达

#### 2. 便捷操作体验
- 💫 微信小程序一键操作
- 🔄 实时更新还款状态
- 📱 随时随地查看贷款信息

#### 3. 专业管理功能
- 📈 多维度数据统计
- 📋 完整的还款计划管理
- 📝 详细的交易记录追踪

#### 4. 安全可靠
- 🔒 银行级数据加密
- 🛡️ 严格的隐私保护
- ⚡ 稳定的系统性能

### 💡 为什么选择随钱记？

1. **省心管理**
   - 自动提醒，不遗漏任何还款日期
   - 智能统计，一目了然的资金状况
   - 便捷操作，随时随地管理贷款

2. **安全可靠**
   - 专业的技术团队支持
   - 完善的数据备份机制
   - 严格的隐私保护措施

3. **持续优化**
   - 定期功能更新
   - 及时响应用户需求
   - 不断提升用户体验

### 🎯 适用人群

- 👨‍💼 个人贷款用户
- 🏢 小微企业主
- 💼 贷款管理人员
- 📊 财务管理人员

### 🌈 我们的愿景

致力于为用户提供最专业、最便捷的贷款管理服务，让贷款管理变得简单而高效。随钱记，您的贴心金融管理助手！

### 🔥 立即开始

现在就扫描小程序码，开启您的智能贷款管理之旅！告别繁琐的手动记账，让随钱记为您的资金管理保驾护航。

---

© 2024 随钱记 版权所有