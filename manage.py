#!/usr/bin/env python
"""Django的命令行工具，用于执行管理任务."""

# 导入操作系统模块，用于设置环境变量
import os
# 导入系统模块，用于处理命令行参数
import sys


def main():
    """程序的入口点."""
    # 设置Django的设置模块环境变量
    os.environ.setdefault('DJANGO_ENV', 'develop')
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    try:
        # 从Django的核心管理模块导入执行命令行功能
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        # 如果导入失败，抛出自定义错误信息，确保用户知道如何解决问题
        raise ImportError(
            "无法导入Django. 确保它已安装并可用在你的PYTHONPATH环境变量中？"
            "你是否忘记激活虚拟环境了？"
        ) from exc
    # 执行从命令行输入的Django管理命令
    execute_from_command_line(sys.argv)


# 检查当前脚本是否作为主模块直接运行，如果是，则执行main函数
if __name__ == '__main__':
    main()
