[supervisord]
nodaemon=true
logfile=/var/log/supervisord.log
pidfile=/var/run/supervisord.pid

[program:gunicorn]
command=gunicorn config.wsgi:application -b 0.0.0.0:8000 --workers=4
directory=/app
user=root
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/gunicorn.log

[program:celery_worker]
command=celery -A config worker -l info
directory=/app
user=root
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/celery_worker.log

[program:celery_beat]
command=celery -A config beat -l info
directory=/app
user=root
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/celery_beat.log 